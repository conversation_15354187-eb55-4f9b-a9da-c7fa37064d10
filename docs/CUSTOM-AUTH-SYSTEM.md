# Custom Authentication System

## Overview

This Laravel API implements a custom authentication system with two types of users:

1. **Named Users** - Identified by unique hash, no credentials required, auto-login with token tracking
2. **Admin Users** - Traditional credential-based authentication (email/password)

## Route Structure

### 🌐 Public Routes (`/api/public/*`)
- **No authentication required**
- Available to everyone
- Examples:
  - `GET /api/public/health` - API health check
  - `GET /api/public/info` - API information

### 👤 User Routes (`/api/user/*`)
- **Requires Named User token**
- Protected by `user.auth` middleware
- For users identified by hash
- Examples:
  - `GET /api/user/profile` - User profile information

### 🔐 Admin Routes (`/api/admin/*`)
- **Requires Admin credentials**
- Protected by `admin.auth` middleware
- For traditional authenticated admin users
- Examples:
  - `POST /api/admin/auth/login` - Admin login
  - `GET /api/admin/dashboard` - Admin dashboard

### 🔄 Auto-Login Endpoint
- `POST /api/auto-login` - Generates token for Named Users

## Middleware Implementation

### UserAuth Middleware (`app/Http/Middleware/UserAuth.php`)

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UserAuth
{
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user has valid token
        if (!$request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'User token required'
            ], 401);
        }

        // TODO: Implement your named user authentication logic here
        // Example logic structure:
        // 1. Validate token
        // 2. Get user from token (by hash)
        // 3. Check if user is named user type
        // 4. Return unauthorized if not valid user

        $isValidUser = $this->checkUserToken($request);

        if (!$isValidUser) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid user token'
            ], 403);
        }

        return $next($request);
    }

    private function checkUserToken(Request $request): bool
    {
        // TODO: Implement your user token checking logic
        // Examples:
        // - Validate token against named users
        // - Check user hash from token
        // - Verify user is active/valid

        return false; // Placeholder - replace with your logic
    }
}
```

### AdminAuth Middleware (`app/Http/Middleware/AdminAuth.php`)

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAuth
{
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated with valid token
        if (!$request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization token required'
            ], 401);
        }

        // TODO: Implement your admin authentication logic here
        // Example logic structure:
        // 1. Validate token
        // 2. Get user from token
        // 3. Check if user is admin type
        // 4. Return unauthorized if not admin

        $isAdmin = $this->checkAdminCredentials($request);

        if (!$isAdmin) {
            return response()->json([
                'success' => false,
                'message' => 'Admin access required'
            ], 403);
        }

        return $next($request);
    }

    private function checkAdminCredentials(Request $request): bool
    {
        // TODO: Implement your admin checking logic
        // Examples:
        // - Validate token against admin users
        // - Check user type from database
        // - Verify admin permissions

        return false; // Placeholder - replace with your logic
    }
}
```

## Implementation Guide

### 1. Middleware Registration

Middleware is registered in `bootstrap/app.php`:

```php
->withMiddleware(function (Middleware $middleware): void {
    $middleware->alias([
        'admin.auth' => \App\Http\Middleware\AdminAuth::class,
        'user.auth' => \App\Http\Middleware\UserAuth::class,
    ]);
})
```

### 2. Route Configuration

Routes are organized in `routes/api.php`:

```php
// Public routes (no auth)
Route::prefix('public')->group(function () {
    // Your public endpoints
});

// Auto-login for Named Users
Route::post('/auto-login', function (Request $request) {
    // Your auto-login logic
});

// User routes (Named User auth)
Route::prefix('user')->middleware('user.auth')->group(function () {
    // Your user endpoints
});

// Admin routes (Admin auth)
Route::prefix('admin')->middleware('admin.auth')->group(function () {
    // Your admin endpoints
});
```

### 3. Custom Logic Implementation

You need to implement the following methods in the middleware:

#### For UserAuth:
- `checkUserToken()` - Validate Named User tokens
- Logic to verify user hash and token validity

#### For AdminAuth:
- `checkAdminCredentials()` - Validate Admin credentials
- Logic to verify admin user type and permissions

### 4. Token Management

#### Named Users:
- Generate tokens via `/api/auto-login` endpoint
- Tokens should be tied to user hash
- No traditional login/logout required

#### Admin Users:
- Use traditional login/logout via `/api/admin/auth/*` endpoints
- Tokens should be tied to admin user credentials

## Testing the System

### Public Endpoints (✅ Working)
```bash
curl http://localhost:8000/api/public/health
curl http://localhost:8000/api/public/info
```

### User Endpoints (🔒 Protected)
```bash
# Without token (401 error)
curl http://localhost:8000/api/user/profile

# With token (implement your logic)
curl -H "Authorization: Bearer YOUR_USER_TOKEN" http://localhost:8000/api/user/profile
```

### Admin Endpoints (🔒 Protected)
```bash
# Without token (401 error)
curl http://localhost:8000/api/admin/dashboard

# With token (implement your logic)
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" http://localhost:8000/api/admin/dashboard
```

## Next Steps

1. **Implement Token Logic**: Replace placeholder `return false;` with your actual authentication logic
2. **Database Structure**: Design your user table to support both user types
3. **Token Generation**: Implement token creation and validation
4. **User Hash Management**: Create system for generating and managing user hashes
5. **Admin Management**: Implement admin user creation and management

## Security Considerations

- Ensure tokens are properly validated and not easily guessable
- Implement token expiration for security
- Use secure hashing for Named User identification
- Implement rate limiting for authentication endpoints
- Log authentication attempts for security monitoring

---

**Status**: ✅ Middleware structure complete - Ready for your custom logic implementation
