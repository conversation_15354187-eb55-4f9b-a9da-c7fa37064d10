# AI Assessment Scoring API Documentation

## Overview
The AI Assessment Scoring API allows you to submit student answers for AI-powered assessment and receive scores with detailed feedback.

## Endpoint
```
POST /api/public/ai-assessments/score
```

## Authentication
This is a public endpoint and does not require authentication.

## Request Format

### Headers
```
Content-Type: application/json
Accept: application/json
```

### Request Body
```json
{
    "unit_id": 1,
    "answers": [
        {
            "assessment_id": 123,
            "answer": "Student's answer text here"
        },
        {
            "assessment_id": 124,
            "answer": "Another student answer"
        }
    ]
}
```

### Parameters
- `unit_id` (integer, required): The ID of the unit containing the assessments
- `answers` (array, required): Array of student answers
  - `assessment_id` (integer, required): The ID of the assessment being answered
  - `answer` (string, required): The student's answer text

### Validation Rules
- `unit_id`: Must be a valid integer that exists in the units table
- `answers`: Must be a non-empty array
- Each answer object must contain both `assessment_id` and `answer` fields

## Response Format

### Success Response (200 OK)
```json
{
    "success": true,
    "message": "AI assessment scored successfully",
    "data": [
        {
            "id": 123,
            "point": "8/10",
            "comment": "Good understanding of the concept. Minor grammatical errors present but meaning is clear."
        },
        {
            "id": 124,
            "point": "6/10", 
            "comment": "Partial understanding shown. Needs improvement in sentence structure and vocabulary usage."
        }
    ]
}
```

### Error Responses

#### 404 Not Found - Unit Not Found
```json
{
    "success": false,
    "message": "Resource not found",
    "error": "No query results for model [App\\Models\\Unit] 999"
}
```

#### 422 Validation Error
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "unit_id": ["The unit id field is required."],
        "answers": ["The answers field is required."]
    }
}
```

#### 500 Internal Server Error
```json
{
    "success": false,
    "message": "Failed to score assessments",
    "error": "An unexpected error occurred while processing your request"
}
```

## cURL Examples

### Basic Request
```bash
curl -X POST "https://your-domain.com/api/public/ai-assessments/score" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "unit_id": 1,
    "answers": [
      {
        "assessment_id": 123,
        "answer": "The cat is sleeping on the mat."
      },
      {
        "assessment_id": 124,
        "answer": "I goes to school every day."
      }
    ]
  }'
```

### Multiple Assessment Answers
```bash
curl -X POST "https://your-domain.com/api/public/ai-assessments/score" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "unit_id": 5,
    "answers": [
      {
        "assessment_id": 201,
        "answer": "She has been studying English for three years."
      },
      {
        "assessment_id": 202,
        "answer": "The weather today is very nice and sunny."
      },
      {
        "assessment_id": 203,
        "answer": "I like to play football with my friends on weekends."
      }
    ]
  }'
```

### Request with Verbose Output
```bash
curl -X POST "https://your-domain.com/api/public/ai-assessments/score" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -v \
  -d '{
    "unit_id": 2,
    "answers": [
      {
        "assessment_id": 150,
        "answer": "The book what I reading is very interesting."
      }
    ]
  }'
```

## Response Data Structure

### Success Response Fields
- `success` (boolean): Always `true` for successful requests
- `message` (string): Human-readable success message
- `data` (array): Array of assessment results

### Assessment Result Fields
- `id` (integer): The assessment ID that was scored
- `point` (string): The score given by AI (format may vary, e.g., "8/10", "B+", "85%")
- `comment` (string): Detailed feedback from the AI about the answer

## Usage Notes

1. **Unit Types**: This endpoint works with AI-enabled assessment types. Ensure the unit contains assessments that support AI scoring.

2. **Answer Format**: Answers should be submitted as plain text. The AI will analyze grammar, vocabulary, sentence structure, and meaning.

3. **Assessment IDs**: Make sure all assessment IDs in the request belong to the specified unit.

4. **Rate Limiting**: Consider implementing rate limiting in production to prevent abuse of the AI scoring service.

5. **Response Time**: AI scoring may take several seconds depending on the number of assessments and complexity of answers.

## Error Handling

Always check the `success` field in the response:
- `success: true` - Request was processed successfully
- `success: false` - An error occurred, check the `message` and `error` fields for details

Common error scenarios:
- Invalid unit ID
- Missing or empty answers array
- Assessment IDs that don't belong to the specified unit
- AI service unavailability
- Network timeouts

## Integration Examples

### JavaScript/Fetch
```javascript
const scoreAssessments = async (unitId, answers) => {
  try {
    const response = await fetch('/api/public/ai-assessments/score', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        unit_id: unitId,
        answers: answers
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Assessment scoring failed:', error);
    throw error;
  }
};
```

### PHP/Laravel HTTP Client
```php
use Illuminate\Support\Facades\Http;

$response = Http::post('https://your-domain.com/api/public/ai-assessments/score', [
    'unit_id' => 1,
    'answers' => [
        [
            'assessment_id' => 123,
            'answer' => 'Student answer here'
        ]
    ]
]);

if ($response->successful() && $response->json('success')) {
    $scores = $response->json('data');
    // Process scores...
} else {
    $error = $response->json('message');
    // Handle error...
}
```

### Python/Requests
```python
import requests

def score_assessments(unit_id, answers):
    url = "https://your-domain.com/api/public/ai-assessments/score"
    payload = {
        "unit_id": unit_id,
        "answers": answers
    }
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    response = requests.post(url, json=payload, headers=headers)
    result = response.json()
    
    if result.get("success"):
        return result["data"]
    else:
        raise Exception(result.get("message", "Unknown error"))
```