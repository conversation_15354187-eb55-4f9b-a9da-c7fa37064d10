# CSV Import API

## Base URL
```
https://your-api-domain.com/api
```

## Authentication
All import endpoints require admin authentication:
```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

---

## Import CSV File for Unit
**Endpoint:** `POST /admin/units/{unit}/import/csv`

Import assessments from a CSV file for a specific unit.

```bash
# Import Multiple Select assessments (default)
curl -X POST "https://your-api-domain.com/api/admin/units/1/import/csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@/path/to/your/multiple_select.csv" \
  -F "assessment_type=multiple_select"

# Import AI Gap Fill Sentence assessments
curl -X POST "https://your-api-domain.com/api/admin/units/1/import/csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "file=@/path/to/your/gap_fill.csv" \
  -F "assessment_type=ai_gap_fill_sentence"
```

**Form Data:**
- `file` - CSV file (required, max 10MB, .csv or .txt format)
- `assessment_type` - Assessment type (optional, default: "multiple_select")
  - `multiple_select` - Multiple choice assessments
  - `ai_gap_fill_sentence` - AI gap fill sentence assessments

**Response:**
```json
{
    "success": true,
    "message": "CSV import completed successfully",
    "data": {
        "summary": {
            "units_created": 3,
            "assessments_created": 12
        },
        "created_units": [
            {
                "id": 10,
                "title": "Unit 1: Basic Greetings",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 1
            },
            {
                "id": 11,
                "title": "Unit 2: Numbers",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 2
            },
            {
                "id": 12,
                "title": "Unit 3: Colors",
                "skill_type": "vocabulary",
                "difficulty": "elementary",
                "unit_order": 3
            }
        ]
    }
}
```

**File Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "csv_file": ["The csv file field is required."],
        "course_id": ["The selected course id is invalid."]
    }
}
```

**CSV Format Error:**
```json
{
    "success": false,
    "message": "CSV validation failed",
    "error": "Missing required headers: unit_title, question"
}
```

**Data Validation Error:**
```json
{
    "success": false,
    "message": "CSV validation failed",
    "error": "Row 3: The skill type 'invalid_skill' is not valid. Row 5: The correct answer 'Wrong Answer' does not match any of the provided options."
}
```

---

## Validate CSV (Dry Run)
**Endpoint:** `POST /admin/import/validate-csv`

Validates CSV file format and data without actually importing.

```bash
curl -X POST "https://your-api-domain.com/api/admin/import/validate-csv" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -F "csv_file=@/path/to/your/import.csv" \
  -F "course_id=1"
```

**Form Data:**
- `csv_file` - CSV file (required, max 10MB, .csv or .txt format)
- `course_id` - Target course ID (required, must exist in courses table)

**Success Response:**
```json
{
    "success": true,
    "message": "CSV file is valid and ready for import",
    "data": {
        "total_rows": 15,
        "estimated_units": 3,
        "estimated_assessments": 15,
        "preview": [
            {
                "unit_title": "Unit 1: Basic Greetings",
                "unit_description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "question": "What is 'hello' in English?",
                "option_1": "Hello",
                "option_2": "Goodbye",
                "option_3": "Please",
                "option_4": "Thanks",
                "correct_answer": "Hello",
                "explanation": "Common greeting used daily"
            },
            {
                "unit_title": "Unit 1: Basic Greetings",
                "unit_description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "question": "How do you say goodbye?",
                "option_1": "Hello",
                "option_2": "Goodbye",
                "option_3": "Please",
                "option_4": "Thanks",
                "correct_answer": "Goodbye",
                "explanation": "Used when leaving"
            },
            {
                "unit_title": "Unit 2: Numbers",
                "unit_description": "Learn numbers 1-20",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "question": "What number comes after 'one'?",
                "option_1": "Two",
                "option_2": "Three",
                "option_3": "Four",
                "option_4": "Five",
                "correct_answer": "Two",
                "explanation": "Basic number sequence"
            }
        ]
    }
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "CSV validation failed",
    "errors": [
        "Missing headers: unit_title, question",
        "Row 2: The skill type field is required",
        "Row 5: The correct answer does not match any option"
    ]
}
```

---

## Download CSV Template
**Endpoint:** `GET /admin/import/template`

Downloads a CSV template file with proper headers and sample data for the specified assessment type.

```bash
# Download Multiple Select template (default)
curl -X GET "https://your-api-domain.com/api/admin/import/template?assessment_type=multiple_select" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -o "multiple_select_template.csv"

# Download AI Gap Fill Sentence template
curl -X GET "https://your-api-domain.com/api/admin/import/template?assessment_type=ai_gap_fill_sentence" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -o "gap_fill_template.csv"
```

**Query Parameters:**
- `assessment_type` - Assessment type (optional, default: "multiple_select")
  - `multiple_select` - Multiple choice assessments template
  - `ai_gap_fill_sentence` - AI gap fill sentence assessments template

**Multiple Select Template Response:**
```csv
question,option_1,option_2,option_3,option_4,option_5,option_6,correct_answer,explanation
"What is the English word for ""hello""?","Hello","Goodbye","Please","Thank you","Sorry","Welcome","Hello","Hello is a common greeting used when meeting someone"
"How do you say ""goodbye"" in English?","Hello","Goodbye","Please","Sorry","","","Goodbye","Goodbye is used when leaving or parting from someone"
```

**AI Gap Fill Sentence Template Response:**
```csv
question,context
"I ____ to the store yesterday.","A sentence about going to a store in the past tense."
"The cat ____ on the mat and ____ peacefully.","A sentence describing a cat's actions with two gaps to fill."
```

**Headers:**
- `Content-Type: text/csv`
- `Content-Disposition: attachment; filename="[type]_import_template.csv"`

**Error Response:**
```json
{
    "success": false,
    "message": "Failed to generate template",
    "error": "Template generation service unavailable"
}
```

---

## CSV Format Requirements

### Required Headers
All headers must be present in the CSV file:

| Header | Required | Max Length | Description |
|--------|----------|------------|-------------|
| `unit_title` | ✅ | 255 chars | Title of the unit |
| `unit_description` | ❌ | 2000 chars | Description of the unit |
| `skill_type` | ✅ | - | vocabulary, grammar, listening, reading, speaking, writing |
| `difficulty` | ✅ | - | beginner, elementary, intermediate, upper_intermediate, advanced, proficient |
| `question` | ✅ | 1000 chars | Assessment question text |
| `option_1` | ✅ | 500 chars | First answer option |
| `option_2` | ✅ | 500 chars | Second answer option |
| `option_3` | ❌ | 500 chars | Third answer option |
| `option_4` | ❌ | 500 chars | Fourth answer option |
| `correct_answer` | ✅ | - | Must match exactly one of the options |
| `explanation` | ❌ | 1000 chars | Explanation for the correct answer |

### Skill Types
Valid values for `skill_type` column:
- `vocabulary` - Vocabulary learning
- `grammar` - Grammar rules and structure
- `listening` - Listening comprehension
- `reading` - Reading comprehension
- `speaking` - Speaking practice
- `writing` - Writing skills

### Difficulty Levels
Valid values for `difficulty` column:
- `beginner` - Beginner level
- `elementary` - Elementary level
- `intermediate` - Intermediate level
- `upper_intermediate` - Upper intermediate level
- `advanced` - Advanced level
- `proficient` - Proficient level

### File Constraints
- **Maximum file size:** 10MB
- **Supported formats:** CSV, TXT
- **Encoding:** UTF-8 recommended
- **Delimiter:** Comma (`,`)
- **Text qualifier:** Double quotes (`"`) for fields containing commas or newlines

### Data Processing Rules

**Unit Grouping:**
- Multiple rows with the same `unit_title` are grouped into one unit
- Unit description uses the first non-empty description found
- Each row creates one assessment within the unit

**Auto-ordering:**
- Units are ordered by appearance in CSV (first occurrence determines order)
- Assessments within units are ordered by row appearance

**Transaction Safety:**
- All-or-nothing import (entire import fails if any row has errors)
- Database rollback on any validation or processing error

**Example CSV Data:**
```csv
unit_title,unit_description,skill_type,difficulty,question,option_1,option_2,option_3,option_4,correct_answer,explanation
"Unit 1: Greetings","Basic greetings","vocabulary","beginner","What is hello?","Hello","Goodbye","","","Hello","Common greeting"
"Unit 1: Greetings","","vocabulary","beginner","How to say bye?","Hello","Goodbye","Thanks","","Goodbye","Farewell greeting"
"Unit 2: Numbers","Learn numbers","vocabulary","beginner","What is 1?","One","Two","Three","Four","One","First number"
```

This creates:
- 2 units: "Unit 1: Greetings" (2 assessments), "Unit 2: Numbers" (1 assessment)
- Total: 3 assessments across 2 units

---

## Import Process Flow

### 1. File Upload & Validation
- File size and format validation
- Course ID existence check
- CSV structure validation (headers)

### 2. Data Parsing & Validation
- Parse CSV rows into structured data
- Validate each field against requirements
- Check skill_type and difficulty enum values
- Verify correct_answer matches one of the options

### 3. Unit Grouping
- Group assessments by unit_title
- Calculate unit ordering based on first appearance
- Prepare unit and assessment data structures

### 4. Database Transaction
- Begin database transaction
- Create units in order
- Create assessments and attach to units
- Commit transaction on success, rollback on error

### 5. Response Generation
- Return summary of created items
- Include detailed unit information
- Provide error details if import fails

---

## Common Error Scenarios

### File Upload Errors
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "csv_file": ["The csv file must be a file of type: csv, txt."],
        "course_id": ["The course id field is required."]
    }
}
```

### File Size Error
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "csv_file": ["The csv file must not be greater than 10240 kilobytes."]
    }
}
```

### Header Validation Error
```json
{
    "success": false,
    "message": "CSV validation failed",
    "errors": [
        "Missing headers: unit_title, question, option_1, option_2, correct_answer"
    ]
}
```

### Data Validation Error
```json
{
    "success": false,
    "message": "CSV validation failed",
    "error": "Row 2: The skill type 'invalid' is not valid. Row 3: The question field is required. Row 5: The correct answer 'Wrong' does not match any of the provided options."
}
```

### Course Not Found Error
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "course_id": ["The selected course id is invalid."]
    }
}
```

### Database Error
```json
{
    "success": false,
    "message": "Failed to import CSV file",
    "error": "Database transaction failed: Duplicate entry for unit title"
}
```

---

## Best Practices

### CSV Preparation
1. **Use UTF-8 encoding** to support international characters
2. **Quote fields** containing commas, quotes, or newlines
3. **Validate data** before upload using the validate endpoint
4. **Test with small files** before importing large datasets
5. **Backup database** before large imports

### Error Handling
1. **Always validate first** using `/validate-csv` endpoint
2. **Check file size** before upload (10MB limit)
3. **Handle network timeouts** for large files
4. **Review error messages** for specific row/field issues

### Performance Tips
1. **Batch imports** for very large datasets (split into multiple files)
2. **Use consistent formatting** to avoid parsing errors
3. **Remove empty rows** to improve processing speed
4. **Monitor import progress** through response data

---

## HTTP Status Codes

- `200` - Success (validation, template download)
- `201` - Import completed successfully
- `400` - Bad request / File validation error
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `422` - CSV validation failed / Data errors
- `429` - Rate limit exceeded
- `500` - Internal server error / Import failed
