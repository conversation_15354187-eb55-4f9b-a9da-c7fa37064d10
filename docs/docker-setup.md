# Docker Setup Guide

## Overview
This Docker Compose setup includes:
- **Backend API**: Laravel application using FrankenPHP
- **MariaDB**: Database server
- **phpMyAdmin**: Web-based database management interface
- **Redis**: Cache and session storage

## Prerequisites
- <PERSON>er and Docker Compose installed
- Your Laravel application key generated

## Quick Start

### 1. Environment Configuration
Update your `.env` file with Docker-appropriate settings:

```bash
# Database Configuration for Docker
DB_HOST=mariadb
DB_DATABASE=backend
DB_USERNAME=root
DB_PASSWORD=secret

# Redis Configuration for Docker
REDIS_HOST=redis
CACHE_STORE=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis
```

### 2. Start Services
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f backend
```

### 3. Initial Setup
```bash
# Generate application key (if not done)
docker-compose exec backend php artisan key:generate

# Run migrations
docker-compose exec backend php artisan migrate

# Clear caches
docker-compose exec backend php artisan config:clear
docker-compose exec backend php artisan cache:clear
```

## Service Details

### Backend API
- **Container**: `backend-api`
- **Port**: 8000
- **URL**: http://localhost:8000

### MariaDB
- **Container**: `backend-mariadb`
- **Port**: 3307 (external), 3306 (internal)
- **Database**: `backend`
- **Root Password**: `secret`
- **User**: `backend_user`
- **Password**: `backend_password`

### phpMyAdmin
- **Container**: `backend-phpmyadmin`
- **Port**: 8080 (external), 80 (internal)
- **Web Interface**: http://localhost:8080
- **Login**: root / secret

### Redis
- **Container**: `backend-redis`
- **Port**: 6380 (external), 6379 (internal)
- **Persistence**: Enabled with AOF

## Useful Commands

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This deletes your database data)
docker-compose down -v

# Rebuild backend container
docker-compose build backend
docker-compose up -d backend

# Access backend container shell
docker-compose exec backend bash

# Access MariaDB
docker-compose exec mariadb mysql -u root -p backend

# Access MariaDB from host (using external port)
mysql -h 127.0.0.1 -P 3307 -u root -p backend

# Access Redis CLI
docker-compose exec redis redis-cli

# View service logs
docker-compose logs backend
docker-compose logs mariadb
docker-compose logs phpmyadmin
docker-compose logs redis
```

## Development Workflow

1. Make code changes in your local files
2. Changes are automatically reflected in the container via volume mounting
3. For dependency changes, rebuild the container:
   ```bash
   docker-compose build backend
   docker-compose up -d backend
   ```

## Troubleshooting

### Database Connection Issues
- Ensure MariaDB is fully started before backend: `docker-compose logs mariadb`
- Check database credentials in `.env` file
- Verify database exists: `docker-compose exec mariadb mysql -u root -p -e "SHOW DATABASES;"`

### Redis Connection Issues
- Check Redis is running: `docker-compose exec redis redis-cli ping`
- Verify Redis host in `.env`: `REDIS_HOST=redis`

### Backend Not Starting
- Check logs: `docker-compose logs backend`
- Ensure all dependencies are installed
- Try rebuilding: `docker-compose build backend`
