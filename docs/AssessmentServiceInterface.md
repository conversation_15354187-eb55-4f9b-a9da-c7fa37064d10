# AssessmentService Interface Design

## Overview

The `AssessmentServiceInterface` provides a unified contract for all assessment type services in the English Game Backend. This interface ensures consistent behavior across different assessment types while maintaining flexibility for type-specific implementations.

## Architecture

### Interface Hierarchy

```
AssessmentServiceInterface
├── AssessmentService (Abstract Base Class)
│   ├── AssessmentMultipleSelectService
│   ├── AssessmentGapFillService
│   └── AssessmentAiGapFillSentenceService
```

### Core Components

1. **AssessmentServiceInterface** - Defines the contract
2. **AssessmentService** - Abstract base implementation
3. **AssessmentScoringResult** - Standardized scoring result format
4. **Concrete Services** - Type-specific implementations

## Interface Contract

### Required Methods

#### CRUD Operations

```php
// Create new assessment with polymorphic Assessment record
public function create(array $data): Model;

// Update existing assessment
public function update(Model $assessment, array $data): Model;

// Delete assessment and cleanup relationships
public function delete(Model $assessment): bool;
```

#### Scoring System

```php
// Score user's answer with standardized result format
public function score(Model $assessment, array $userAnswer): array;
```

#### Unit Relationship Management

```php
// Attach assessment to units with ordering
public function attachToUnits(Model $assessment, array $unitAttachments): void;

// Detach assessment from specified units (or all)
public function detachFromUnits(Model $assessment, array $unitIds = []): void;
```

## Standardized Scoring Format

### Current Legacy Format
```php
[
    'pass' => bool,           // Whether assessment was passed
    'explain' => string,      // Explanation/feedback
    'meta' => mixed,          // Optional metadata
    'point' => string,        // AI scoring (for AI assessments)
    'comment' => string       // Alternative to explain
]
```

### Recommended Future Format
```php
[
    'passed' => bool,         // Whether assessment was passed
    'explanation' => string,  // Unified explanation field
    'metadata' => array       // Structured metadata
]
```

## Implementation Examples

### Multiple Select Assessment

```php
class AssessmentMultipleSelectService extends AssessmentService
{
    public function create(array $data): AssessmentMultipleSelect
    {
        $assessment = AssessmentMultipleSelect::create([
            'question' => $data['question'],
            'answer_list' => $data['answer_list'],
            'correct_answer_indexes' => $data['correct_answer_indexes'],
            'explanations' => $data['explanations'] ?? [],
        ]);

        return $this->createAssessmentRecord($assessment);
    }

    public function score(AssessmentMultipleSelect $assessment, array $userAnswer): array
    {
        $correctIndexes = $assessment->correct_answer_indexes;
        $userSelectedIndexes = array_map('intval', $userAnswer);
        sort($userSelectedIndexes);
        
        $correctIndexesSorted = array_map('intval', $correctIndexes);
        sort($correctIndexesSorted);
        
        $isCorrect = $userSelectedIndexes === $correctIndexesSorted;
        
        return [
            'pass' => $isCorrect,
            'explain' => $isCorrect ? 'Correct!' : 'Incorrect selection.'
        ];
    }
}
```

### Gap Fill Assessment

```php
class AssessmentGapFillService extends AssessmentService
{
    public function create(array $data): AssessmentGapFill
    {
        $assessment = AssessmentGapFill::create([
            'question' => $data['question'],
            'explanation' => $data['explanation'] ?? null,
            'correct_answers' => $data['correct_answers'],
        ]);

        return $this->createAssessmentRecord($assessment);
    }

    public function score(AssessmentGapFill $assessment, array $userAnswer): array
    {
        $correctAnswers = $assessment->correct_answers;
        $gapCount = count($correctAnswers);
        $correctCount = 0;

        for ($i = 0; $i < $gapCount; $i++) {
            $userAnswerText = trim(strtolower($userAnswer[$i] ?? ''));
            $correctOptions = array_map('trim', array_map('strtolower', (array)$correctAnswers[$i]));
            
            if (in_array($userAnswerText, $correctOptions)) {
                $correctCount++;
            }
        }

        $isCorrect = $correctCount === $gapCount;
        
        return [
            'pass' => $isCorrect,
            'explain' => $isCorrect ? 'All gaps correct!' : "Correct: {$correctCount}/{$gapCount} gaps"
        ];
    }
}
```

### AI Gap Fill Sentence Assessment

```php
class AssessmentAiGapFillSentenceService extends AssessmentService
{
    public function create(array $data): AssessmentAiGapFillSentence
    {
        $assessment = AssessmentAiGapFillSentence::create([
            'question' => $data['question'],  // Must contain underscores for gaps
            'context' => $data['context'],
        ]);

        return $this->createAssessmentRecord($assessment);
    }

    public function score(AssessmentAiGapFillSentence $assessment, array $userAnswer): array
    {
        // Uses AI service for scoring
        $aiPromptService = app(AiPromptService::class);
        $unit = $assessment->assessment->units->first();
        
        // Build completed sentence
        $completedSentence = $this->buildCompletedSentence(
            $assessment->question, 
            $userAnswer, 
            $assessment->fill_position
        );
        
        // Get AI scoring
        $result = $aiPromptService->getResult($unit->id, [[
            'assessment_id' => $assessment->assessment->id,
            'answer' => $completedSentence
        ]]);
        
        $assessmentResult = collect($result['ai_output'])
            ->firstWhere('id', $assessment->assessment->id);
        
        return [
            'pass' => (float) explode('/', $assessmentResult['point'])[0] >= 8,
            'explain' => $assessmentResult['comment'],
        ];
    }
}
```

## Usage Patterns

### Service Registration

Register services in your service container:

```php
// AppServiceProvider
$this->app->bind(AssessmentServiceInterface::class, function ($app, $parameters) {
    $unitType = $parameters['unitType'] ?? null;
    
    return match ($unitType) {
        UnitType::MULTIPLE_SELECT => $app->make(AssessmentMultipleSelectService::class),
        UnitType::GAP_FILL => $app->make(AssessmentGapFillService::class),
        UnitType::AI_GAP_FILL_SENTENCE => $app->make(AssessmentAiGapFillSentenceService::class),
        default => throw new InvalidArgumentException("Unsupported unit type: {$unitType}")
    };
});
```

### Controller Integration

```php
class AssessmentController extends Controller
{
    private function getService(UnitType $unitType): AssessmentServiceInterface
    {
        return match ($unitType) {
            UnitType::MULTIPLE_SELECT => $this->multipleSelectService,
            UnitType::AI_GAP_FILL_SENTENCE => $this->aiGapFillService,
            UnitType::GAP_FILL => $this->gapFillService,
        };
    }
    
    public function store(Request $request): JsonResponse
    {
        $unitType = $this->parseAssessmentType($request->input('type'));
        $service = $this->getService($unitType);
        
        $assessment = $service->create($request->validated());
        
        return response()->json([
            'success' => true,
            'message' => 'Assessment created successfully',
            'data' => $assessment
        ], 201);
    }
}
```

## Data Validation

### Common Validation Rules

```php
// Base validation for all assessment types
'unit_attachments' => 'nullable|array',
'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
'unit_attachments.*.assessment_order' => 'nullable|integer|min:1'

// Type-specific validation
UnitType::MULTIPLE_SELECT => [
    'question' => 'required|string|max:1000',
    'answer_list' => 'required|array|min:2|max:10',
    'answer_list.*' => 'required|string|max:500',
    'correct_answer_indexes' => 'required|array|min:1',
    'correct_answer_indexes.*' => 'required|integer|min:0',
    'explanations' => 'nullable|array',
    'explanations.*' => 'nullable|string|max:1000',
],

UnitType::GAP_FILL => [
    'question' => 'required|string|max:1000',
    'explanation' => 'nullable|string|max:2000',
    'correct_answers' => 'required|array|min:1',
    'correct_answers.*' => 'required|string|max:500',
],

UnitType::AI_GAP_FILL_SENTENCE => [
    'question' => 'required|string|max:1000|regex:/.*_.*/',  // Must contain underscores
    'context' => 'required|string|max:2000',
]
```

## Error Handling

### Standard Exceptions

```php
// Interface contract violations
throw new \InvalidArgumentException('Assessment answers must be provided as an array');

// Model not found
throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Assessment not found');

// Service-specific errors
throw new \Exception('AI service did not return result for this assessment');
```

### Transaction Safety

All services use database transactions for data integrity:

```php
public function delete(Model $assessmentModel): bool
{
    try {
        DB::beginTransaction();
        
        // Delete relationships first
        $assessment = Assessment::where('itemable_type', get_class($assessmentModel))
            ->where('itemable_id', $assessmentModel->id)
            ->first();
            
        if ($assessment) {
            $assessment->units()->detach();
            $assessment->delete();
        }
        
        // Delete the concrete assessment
        $result = $assessmentModel->delete();
        
        DB::commit();
        return $result === true || $result === 1;
        
    } catch (\Exception $e) {
        DB::rollBack();
        throw $e;
    }
}
```

## Migration Path

### Existing Services
All existing assessment services already extend `AssessmentService` and implement the required methods. No changes needed for basic compatibility.

### Future Enhancements
1. **Standardized Scoring Result**: Implement `AssessmentScoringResult` class for consistent response format
2. **Validation Abstraction**: Move validation rules into service classes
3. **Event System**: Add assessment lifecycle events (created, updated, scored, deleted)
4. **Caching Layer**: Add result caching for expensive AI operations

## Benefits

1. **Consistency**: Unified interface ensures all assessment types behave predictably
2. **Extensibility**: Easy to add new assessment types by implementing the interface
3. **Maintainability**: Clear separation of concerns with type-specific logic
4. **Testability**: Interface allows for easy mocking and testing
5. **Documentation**: Self-documenting code through interface contracts
6. **Type Safety**: PHP type hints ensure compile-time safety

## Testing Strategy

```php
// Interface compliance testing
abstract class AssessmentServiceTestCase extends TestCase
{
    abstract protected function getService(): AssessmentServiceInterface;
    abstract protected function getValidData(): array;
    abstract protected function getValidAnswer(): array;
    
    public function test_implements_interface()
    {
        $this->assertInstanceOf(AssessmentServiceInterface::class, $this->getService());
    }
    
    public function test_can_create_assessment()
    {
        $assessment = $this->getService()->create($this->getValidData());
        $this->assertInstanceOf(Model::class, $assessment);
        $this->assertTrue($assessment->exists);
    }
    
    public function test_can_score_assessment()
    {
        $assessment = $this->getService()->create($this->getValidData());
        $result = $this->getService()->score($assessment, $this->getValidAnswer());
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('pass', $result);
        $this->assertArrayHasKey('explain', $result);
    }
}
```

This interface design provides a solid foundation for the assessment system while maintaining backward compatibility and enabling future enhancements.