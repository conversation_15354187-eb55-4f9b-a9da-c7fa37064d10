# Assessment System Architecture

## Overview

The English Game Backend implements a flexible, polymorphic assessment system that supports multiple question types through a clean separation of concerns. The architecture uses <PERSON><PERSON>'s polymorphic relationships to enable extensible assessment types while maintaining data integrity and performance.

## Core Architecture

### Enum Architecture Layer

The system uses PHP 8.1+ backed enums for type safety and consistent value management across the application.

#### UnitType Enum (Assessment Type Coordinator)

**Purpose**: Central enum that coordinates assessment types with their corresponding models and services

```php
enum UnitType: string {
    case AI_GAP_FILL_SENTENCE = 'ai_gap_fill_sentence';
    case MULTIPLE_SELECT = 'multiple_select';
    case GAP_FILL = 'gap_fill';
}
```

**Key Methods**:

**Model Class Resolution**:
```php
public static function getAssessmentModelClass(string $value): string {
    $unitType = self::from($value);

    return match ($unitType) {
        self::AI_GAP_FILL_SENTENCE => \App\Models\AssessmentAiGapFillSentence::class,
        self::MULTIPLE_SELECT => \App\Models\AssessmentMultipleSelect::class,
        self::GAP_FILL => \App\Models\AssessmentGapFill::class,
    };
}
```

**Reverse Model Lookup**:
```php
public static function fromAssessmentModelClass(string $modelClass): self {
    return match ($modelClass) {
        \App\Models\AssessmentAiGapFillSentence::class => self::AI_GAP_FILL_SENTENCE,
        \App\Models\AssessmentMultipleSelect::class => self::MULTIPLE_SELECT,
        \App\Models\AssessmentGapFill::class => self::GAP_FILL,
        default => throw new \InvalidArgumentException("Unknown model: {$modelClass}"),
    };
}
```

**AI Type Detection**:
```php
public static function getAiAssessmentType(): array {
    return [self::AI_GAP_FILL_SENTENCE->value];
}
```

**Frontend Integration**:
```php
public static function getTypesWithDescriptions(): array {
    return [
        self::AI_GAP_FILL_SENTENCE->value => 'AI Gap Fill Sentence',
        self::MULTIPLE_SELECT->value => 'Multiple Select',
        self::GAP_FILL->value => 'Gap Fill',
    ];
}

public static function getSettings(): array {
    return [
        self::MULTIPLE_SELECT->value => [
            'name' => 'Multiple Select',
            'value' => self::MULTIPLE_SELECT->value,
        ],
        // ... other types
    ];
}
```



#### Service Layer Integration
```php
// Import Controller using UnitType enum
class ImportController extends Controller {
    private function getImportService(string $assessmentType): XlsxImportService {
        $unitType = UnitType::from($assessmentType);

        return match($unitType) {
            UnitType::MULTIPLE_SELECT => $this->multiSelectImportService,
            UnitType::GAP_FILL => $this->gapFillImportService,
            UnitType::AI_GAP_FILL_SENTENCE => $this->aiGapFillImportService,
        };
    }
}
```

### Polymorphic Design Pattern

The system uses a **polymorphic one-to-one relationship** where:
- `Assessment` is the polymorphic base model
- Concrete assessment types (`AssessmentMultipleSelect`, `AssessmentGapFill`, `AssessmentAiGapFillSentence`) are the implementable models
- Each concrete type has exactly one corresponding `Assessment` record

```
Assessment (Base)          Concrete Types
├── itemable_type  ────────► AssessmentMultipleSelect
├── itemable_id            ├── AssessmentGapFill
└── relationships          └── AssessmentAiGapFillSentence
```

### Database Schema

#### Assessment Table (Polymorphic Base)
```sql
assessments
├── id (Primary Key)
├── itemable_type (Class name: App\Models\AssessmentMultipleSelect)
├── itemable_id (Foreign Key to concrete type)
├── created_at
└── updated_at

UNIQUE INDEX: (itemable_type, itemable_id)
```

#### Concrete Assessment Types

**AssessmentMultipleSelect**
```sql
assessment_multiple_selects
├── id (Primary Key)
├── question (TEXT)
├── answer_list (JSON Array)
├── correct_answer_indexes (JSON Array)
├── explanations (JSON Array)
├── created_at
└── updated_at
```

**AssessmentGapFill**
```sql
assessment_gap_fills
├── id (Primary Key)
├── question (TEXT)
├── explanation (TEXT)
├── correct_answers (JSON Array)
├── created_at
└── updated_at
```

**AssessmentAiGapFillSentence**
```sql
assessment_ai_gap_fill_sentences
├── id (Primary Key)
├── question (TEXT)
├── context (TEXT)
├── fill_position (JSON Array - Auto-calculated)
├── created_at
└── updated_at
```

## Model Architecture

### Assessment Base Model

**Purpose**: Polymorphic coordinator and relationship manager

**Key Responsibilities**:
- Manages polymorphic relationship to concrete types
- Provides unified interface for all assessment types
- Handles unit relationships via pivot table
- Implements type-aware helper methods

**Core Methods**:
```php
// Polymorphic relationship
public function itemable(): MorphTo

// Unit relationships
public function units(): BelongsToMany

// Type-aware content access
public function getContext(): string
public function getQuestion(): string
public function getAssessmentType(): string
public function isType(string $type): bool
```

**Performance Optimization**:
```php
// Class mapping for fast type identification
private const TYPE_CLASS_MAP = [
    'App\\Models\\AssessmentAiGapFillSentence' => 'ai_gap_fill',
    'App\\Models\\AssessmentMultipleSelect' => 'multiple_select',
    'App\\Models\\AssessmentGapFill' => 'gap_fill',
];
```

### Concrete Assessment Types

#### 1. AssessmentMultipleSelect

**Purpose**: Multiple choice questions with single or multiple correct answers

**Data Structure**:
```php
[
    'question' => 'What is the capital of France?',
    'answer_list' => ['London', 'Berlin', 'Paris', 'Madrid'],
    'correct_answer_indexes' => [2], // 0-based indexes
    'explanations' => ['Paris is the capital and largest city of France.']
]
```

**Features**:
- JSON-based answer storage
- Multiple correct answers support
- Explanation system
- Auto-shuffling capabilities

#### 2. AssessmentGapFill

**Purpose**: Fill-in-the-blank questions with predefined correct answers

**Data Structure**:
```php
[
    'question' => 'The capital of France is _____.',
    'correct_answers' => ['Paris', 'paris'], // Accept variations
    'explanation' => 'Paris is the capital city of France.'
]
```

**Features**:
- Multiple acceptable answers
- Case-insensitive matching support
- Simple text-based gaps

#### 3. AssessmentAiGapFillSentence

**Purpose**: AI-powered gap-fill with automatic position detection

**Data Structure**:
```php
[
    'question' => 'I ____ to the store and bought _____ apples.',
    'context' => 'Shopping trip context for AI scoring',
    'fill_position' => [2, 35] // Auto-calculated positions
]
```

**Advanced Features**:
- Automatic gap position calculation
- Multiple gaps per sentence
- AI-powered scoring integration
- Context-aware evaluation

**Auto-calculation Logic**:
```php
// Finds underscore sequences and calculates positions
protected static function boot() {
    static::creating(function ($model) {
        $model->fill_position = $model->calculateFillPositions($model->question);
    });
}
```

## Relationship Architecture

### Unit-Assessment Relationships

**Many-to-Many with Pivot**:
```
Unit ←→ unit_assessments ←→ Assessment
     └── assessment_order (ordering)
```

**Implementation**:
```php
// Unit Model
public function assessments(): BelongsToMany {
    return $this->belongsToMany(Assessment::class, 'unit_assessments')
        ->withPivot('assessment_order')
        ->orderByPivot('assessment_order');
}

// Assessment Model
public function units(): BelongsToMany {
    return $this->belongsToMany(Unit::class, 'unit_assessments')
        ->withPivot('assessment_order')
        ->orderByPivot('assessment_order');
}
```

### Polymorphic Relationships

**One-to-One Polymorphic**:
```php
// Assessment (Polymorphic Parent)
public function itemable(): MorphTo {
    return $this->morphTo();
}

// Concrete Types (Polymorphic Children)
public function assessment(): MorphOne {
    return $this->morphOne(Assessment::class, 'itemable');
}
```

## Type System & Performance

### Type Identification

**Fast Type Lookup**:
```php
public function getAssessmentType(): string {
    return self::TYPE_CLASS_MAP[$this->itemable_type] ?? 'unknown';
}
```

**Type Checking**:
```php
$assessment->isType('multiple_select'); // Boolean check
$assessment->getAssessmentType(); // String type
```

### Query Optimization

**Eager Loading**:
```php
// Load concrete types efficiently
Assessment::with('itemable', 'units.course')->get();

// Type-specific queries
Assessment::whereHasMorph('itemable', AssessmentMultipleSelect::class)->get();
```

## Data Integrity & Constraints

### Database Constraints

1. **Unique Polymorphic Pairs**: `UNIQUE(itemable_type, itemable_id)`
2. **No Foreign Key Constraints**: Flexible schema evolution
3. **JSON Validation**: Application-level validation for JSON fields

### Model-Level Integrity

```php
// Automatic fill position calculation
protected static function boot() {
    parent::boot();

    static::creating(function ($model) {
        $model->fill_position = $model->calculateFillPositions($model->question);
    });
}
```

## Import Service Architecture

### Bulk Import System

The system provides a robust bulk import architecture for creating assessments from XLSX files. The import system follows the same polymorphic pattern as the assessment types, with dedicated import services for each assessment type.

#### Import Service Hierarchy

```
XlsxImportService (Abstract Base)
├── XlsxImportAssessmentMultiSelectService
├── XlsxImportAssessmentGapFillService
└── XlsxImportAssessmentAiGapFillSentenceService
```

### Abstract Base: XlsxImportService

**Purpose**: Provides common import functionality and enforces consistent import patterns

**Key Features**:
- Streaming file processing for large datasets
- Memory-efficient batch processing
- Transaction safety (all-or-nothing imports)
- Comprehensive validation pipeline
- Template generation capabilities

**Core Architecture**:
```php
abstract class XlsxImportService {
    // Constants
    const DEFAULT_BATCH_SIZE = 100;
    const MEMORY_THRESHOLD_MB = 128;

    // Abstract methods for type-specific implementation
    abstract protected function getRequiredHeaders(): array;
    abstract protected function validateAssessmentRow(array $row, int $rowNumber): void;
    abstract protected function createAssessmentForUnit(array $row, Unit $unit): Model;
    abstract protected function getAssessmentService(): AssessmentService;
    abstract public function generateTemplateData(): array;
}
```

**Import Workflow**:
1. **File Validation**: Size, format, and structure checks
2. **Header Validation**: Required columns and data types
3. **Streaming Processing**: Memory-efficient row-by-row processing
4. **Batch Creation**: Transaction-safe batch processing
5. **Unit Assignment**: Automatic unit creation and assessment ordering
6. **Validation**: Comprehensive data validation with detailed error reporting

### Type-Specific Import Services

#### XlsxImportAssessmentMultiSelectService

**Template Structure**:
```csv
question,option_1,option_2,option_3,option_4,option_5,option_6,correct_answer,explanation
"What is English for 'hello'?","Hello","Goodbye","Please","Thank you","Sorry","Welcome","Hello","Common greeting"
```

**Key Features**:
- Dynamic option field detection (`option_1` to `option_N`)
- Minimum 2 options required, up to unlimited options
- Automatic correct answer index calculation
- Duplicate option detection and warnings
- Retry logic for transient failures

**Validation Rules**:
```php
$rules = [
    'question' => 'required|string|max:1000',
    'option_1' => 'required|string|max:500',
    'option_2' => 'required|string|max:500',
    'option_3' => 'nullable|string|max:500', // Optional
    'correct_answer' => 'required|string',
    'explanation' => 'nullable|string|max:1000'
];
```

#### XlsxImportAssessmentGapFillService

**Template Structure**:
```csv
question,correct_answer,explanation
"The capital of France is _____.","Paris","Paris is the capital city"
```

**Key Features**:
- Simple gap-fill format with underscore placeholders
- Multiple correct answer variations support
- Context-aware validation
- Case-insensitive answer matching

#### XlsxImportAssessmentAiGapFillSentenceService

**Template Structure**:
```csv
question,context
"I ____ to the store yesterday.","Shopping context for AI scoring"
```

**Key Features**:
- Automatic gap position calculation
- AI context integration
- Multiple gap support in single sentence
- Position-based scoring preparation

### Import Controller Integration

**Service Selection Pattern**:
```php
private function getImportService(string $assessmentType): XlsxImportService {
    return match($assessmentType) {
        'multiple_select' => $this->multiSelectImportService,
        'gap_fill' => $this->gapFillImportService,
        'ai_gap_fill' => $this->aiGapFillImportService,
        default => throw new InvalidArgumentException("Unsupported type: $assessmentType")
    };
}
```

**Import Process**:
1. **Type Validation**: Ensure valid assessment type
2. **File Validation**: Size, format, and accessibility checks
3. **Service Delegation**: Route to appropriate import service
4. **Progress Tracking**: Real-time import progress and statistics
5. **Error Handling**: Comprehensive error collection and reporting
6. **Template Generation**: Dynamic template creation per type

### Advanced Import Features

#### Memory Management
```php
// Stream processing for large files
public function processImportStreaming(UploadedFile $file, array $config): array {
    $reader = IOFactory::createReader('Xlsx');
    $reader->setReadDataOnly(true);
    $reader->setReadEmptyCells(false);

    // Process in chunks to manage memory
    $chunkFilter = new ReadFilter();
    $reader->setReadFilter($chunkFilter);
}
```

#### Batch Processing
```php
// Transaction-safe batch creation
private function processBatch(array $batch, Unit $unit): array {
    return DB::transaction(function() use ($batch, $unit) {
        $created = [];
        foreach ($batch as $row) {
            $created[] = $this->createAssessmentForUnit($row, $unit);
        }
        return $created;
    });
}
```

#### Validation Pipeline
```php
// Multi-level validation
protected function validateAssessmentRow(array $row, int $rowNumber): void {
    $errors = [];
    $warnings = [];

    // Basic field validation
    $validator = Validator::make($row, $this->getValidationRules());

    // Business logic validation
    $this->validateBusinessRules($row, $errors, $warnings);

    // Log warnings, throw errors
    if (!empty($warnings)) Log::info("Row $rowNumber warnings", $warnings);
    if (!empty($errors)) throw new InvalidArgumentException("Row $rowNumber: " . implode(', ', $errors));
}
```

### Template Generation System

Each import service generates type-specific Excel templates:

**Template Components**:
```php
public function generateTemplateData(): array {
    return [
        'headers' => $this->getTemplateHeaders(),
        'sample_data' => $this->getSampleData(),
        'filename' => $this->getTemplateFilename(),
        'instructions' => $this->getImportInstructions()
    ];
}
```

**Dynamic Excel Generation**:
- Formatted headers with data validation
- Sample data with realistic examples
- Cell formatting and protection
- Instructions and guidelines

### Error Handling & Recovery

#### Validation Error Reporting
```php
// Detailed error context
throw new InvalidArgumentException(
    "Row {$rowNumber}: " . implode(', ', [
        'Question too short (minimum 10 characters)',
        'Correct answer must match one of the provided options',
        'Duplicate options detected'
    ])
);
```

#### Retry Logic
```php
// Transient failure recovery
private function createAssessmentWithRetry(array $data, int $maxRetries = 3): Model {
    for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
        try {
            return $this->assessmentService->create($data);
        } catch (Exception $e) {
            if ($attempt < $maxRetries) {
                Log::warning("Assessment creation attempt {$attempt} failed, retrying");
                usleep(100000); // 100ms delay
            } else {
                throw $e;
            }
        }
    }
}
```

## Extension Patterns

### Adding New Assessment Types

1. **Create Model**: Extend `Model`, implement `morphOne` relationship
2. **Create Migration**: Standard Laravel migration
3. **Update Type Map**: Add to `Assessment::TYPE_CLASS_MAP`
4. **Implement Service**: Follow existing service patterns
5. **Add Controller**: Standard CRUD controller
6. **Create Import Service**: Extend `XlsxImportService`
7. **Update Import Controller**: Add service to constructor and routing

**Example New Type**:
```php
class AssessmentEssay extends Model {
    protected $fillable = ['prompt', 'max_words', 'rubric'];

    public function assessment(): MorphOne {
        return $this->morphOne(Assessment::class, 'itemable');
    }
}

class XlsxImportAssessmentEssayService extends XlsxImportService {
    protected function getRequiredHeaders(): array {
        return ['prompt', 'max_words'];
    }

    protected function validateAssessmentRow(array $row, int $rowNumber): void {
        // Essay-specific validation
    }

    protected function createAssessmentForUnit(array $row, Unit $unit): Model {
        // Essay creation logic
    }
}
```

## Service Layer Architecture

### AssessmentServiceInterface Contract

The service layer is built on a standardized contract that all assessment services must implement:

```php
interface AssessmentServiceInterface {
    // CRUD Operations
    public function create(array $data): Model;
    public function update(Model $assessment, array $data): Model;
    public function delete(Model $assessment): bool;

    // Scoring System
    public function score(Model $assessment, array $userAnswer): array;

    // Unit Relationships
    public function attachToUnits(Model $assessment, array $unitAttachments): void;
    public function detachFromUnits(Model $assessment, array $unitIds = []): void;
}
```

**Key Responsibilities**:
- **CRUD Operations**: Create, update, delete assessments with polymorphic coordination
- **Scoring Functionality**: Type-specific answer evaluation and feedback generation
- **Unit Management**: Handle many-to-many relationships with proper ordering

### Abstract Base: AssessmentService

**Purpose**: Provides common functionality for all assessment types while enforcing the interface contract

```php
abstract class AssessmentService implements AssessmentServiceInterface {
    // Concrete implementations
    protected function createAssessmentRecord(Model $assessmentModel): Model;
    public function delete(Model $assessmentModel): bool;
    public function attachToUnits(Model $assessmentModel, array $unitAttachments): void;
    public function detachFromUnits(Model $assessmentModel, array $unitIds = []): void;

    // Abstract methods for type-specific implementation
    abstract public function create(array $data): Model;
    abstract public function update(Model $assessment, array $data): Model;
    abstract public function score(Model $assessment, array $userAnswer): array;
}
```

**Polymorphic Coordination**:
```php
protected function createAssessmentRecord(Model $assessmentModel): Model {
    Assessment::create([
        'itemable_type' => get_class($assessmentModel),
        'itemable_id' => $assessmentModel->id,
    ]);

    return $assessmentModel->load('assessment');
}
```

**Transaction-Safe Deletion**:
```php
public function delete(Model $assessmentModel): bool {
    DB::beginTransaction();
    try {
        // Find and detach from units
        $assessment = Assessment::where('itemable_type', get_class($assessmentModel))
            ->where('itemable_id', $assessmentModel->id)->first();

        if ($assessment) {
            $assessment->units()->detach();
            $assessment->delete();
        }

        $result = $assessmentModel->delete();
        DB::commit();
        return $result === true || $result === 1;
    } catch (Exception $e) {
        DB::rollBack();
        throw $e;
    }
}
```

**Unit Attachment with Auto-Ordering**:
```php
public function attachToUnits(Model $assessmentModel, array $unitAttachments): void {
    foreach ($unitAttachments as $attachment) {
        if (!isset($attachment['assessment_order'])) {
            // Auto-generate next order
            $maxOrder = DB::table('unit_assessments')
                ->where('unit_id', $attachment['unit_id'])
                ->max('assessment_order');
            $assessmentOrder = ($maxOrder ?? 0) + 1;
        }

        $assessment->units()->attach($unitId, [
            'assessment_order' => $assessmentOrder
        ]);
    }
}
```

### Type-Specific Service Implementations

#### AssessmentMultipleSelectService

**Create Pattern**:
```php
public function create(array $data): Model {
    $assessment = AssessmentMultipleSelect::create($data);
    return $this->createAssessmentRecord($assessment);
}
```

**Scoring Algorithm**:
```php
public function score(Model $assessment, array $userAnswer): array {
    $correctIndexes = $assessment->correct_answer_indexes;
    $userSelectedIndexes = array_map('intval', $userAnswer);
    sort($userSelectedIndexes);

    $correctIndexesSorted = array_map('intval', $correctIndexes);
    sort($correctIndexesSorted);

    $isCorrect = $userSelectedIndexes === $correctIndexesSorted;

    return [
        'explain' => $this->generateExplanation($assessment, $correctIndexes),
        'pass' => $isCorrect,
        'meta' => null
    ];
}
```

**Features**:
- Exact match scoring (all correct answers must be selected)
- Auto-generated explanations when not provided
- Support for multiple correct answers

#### AssessmentGapFillService

**Scoring Algorithm**:
```php
public function score(Model $assessment, array $userAnswer): array {
    $correctAnswers = array_map('strtolower', array_map('trim', $assessment->correct_answers));
    $userAnswerLower = strtolower(trim($userAnswer[0] ?? ''));

    $isCorrect = in_array($userAnswerLower, $correctAnswers);

    return [
        'explain' => $assessment->explanation ?? 'Correct answer: ' . $assessment->correct_answers[0],
        'pass' => $isCorrect,
        'meta' => null
    ];
}
```

**Features**:
- Case-insensitive matching
- Multiple acceptable answer variations
- Trim whitespace handling

#### AssessmentAiGapFillSentenceService

**Create with Auto-Calculation**:
```php
public function create(array $data): Model {
    $assessment = AssessmentAiGapFillSentence::create($data);
    // fill_position is auto-calculated via model boot method
    return $this->createAssessmentRecord($assessment);
}
```

**AI Integration Scoring**:
```php
public function score(Model $assessment, array $userAnswer): array {
    // Delegates to AI scoring service for semantic evaluation
    return [
        'explain' => 'AI-powered evaluation based on context',
        'pass' => $this->evaluateWithAI($assessment, $userAnswer),
        'meta' => [
            'ai_confidence' => 0.95,
            'gap_positions' => $assessment->fill_position
        ]
    ];
}
```

**Features**:
- Automatic gap position detection
- AI-powered semantic scoring
- Context-aware evaluation
- Multiple gap support

### Standardized Scoring Response

All services return a consistent scoring format:

```php
[
    'explain' => string,  // Human-readable explanation
    'pass' => boolean,    // Whether the answer is correct
    'meta' => array|null  // Additional metadata (optional)
]
```

**Explanation Strategies**:
- **Multiple Select**: Lists correct answers or uses provided explanation
- **Gap Fill**: Shows correct answer or custom explanation
- **AI Gap Fill**: Context-aware AI-generated feedback

### Service Registration and Dependency Injection

**Service Provider Registration**:
```php
// AppServiceProvider
$this->app->bind(AssessmentMultipleSelectService::class);
$this->app->bind(AssessmentGapFillService::class);
$this->app->bind(AssessmentAiGapFillSentenceService::class);
```

**Controller Integration**:
```php
class AssessmentMultipleSelectController extends Controller {
    public function __construct(
        private AssessmentMultipleSelectService $service
    ) {}

    public function store(StoreAssessmentRequest $request): JsonResponse {
        return $this->executeWithErrorHandling(
            fn() => $this->successResponse(
                'Assessment created successfully',
                $this->service->create($request->validated()),
                201
            ),
            'create assessment'
        );
    }
}
```

### Service Layer Integration

Each assessment type has dedicated services following the pattern:
- `AssessmentMultipleSelectService`
- `AssessmentGapFillService`
- `AssessmentAiGapFillSentenceService`

**Common Service Methods**:
```php
public function create(array $data): Model
public function update(Model $model, array $data): Model
public function delete(Model $model): bool
```

## Performance Considerations

### Query Optimization
- Use eager loading for polymorphic relationships
- Type-specific indexes on concrete tables
- Efficient JSON querying where supported

### Memory Management
- Lazy loading for large assessment collections
- Pagination for admin interfaces
- Selective field loading

### Caching Strategy
- Cache frequently accessed assessment metadata
- Redis caching for type mappings
- Query result caching for static content

## Best Practices

### Data Access
1. Always use `Assessment` as entry point
2. Load concrete types via `itemable` relationship
3. Use type checking before accessing type-specific methods
4. Implement proper error handling for missing concrete types

### Content Management
1. Validate JSON structure at application level
2. Sanitize user input before storage
3. Implement versioning for assessment content
4. Use database transactions for multi-table operations

### Performance
1. Use eager loading for known relationships
2. Implement proper indexing strategies
3. Consider read replicas for assessment delivery
4. Monitor query performance regularly

## Security Considerations

### Data Protection
- Input validation and sanitization
- JSON structure validation
- SQL injection prevention via Eloquent ORM
- Access control through middleware

### Assessment Integrity
- Immutable assessment IDs after creation
- Audit trails for content changes
- Version control for assessment modifications
- Secure answer storage and transmission

---

This architecture provides a flexible, maintainable, and performant foundation for the assessment system while supporting future extensibility and maintaining data integrity.
