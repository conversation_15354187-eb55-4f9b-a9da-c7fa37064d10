version: '3.8'

services:
  # Backend API Service for Development
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backend-api-dev
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_DATABASE=backend
      - DB_USERNAME=root
      - DB_PASSWORD=secret
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CACHE_STORE=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=redis
      - OCTANE_HTTPS=false
      - XDEBUG_MODE=debug
      - XDEBUG_CONFIG=client_host=host.docker.internal client_port=9003 start_with_request=yes
    volumes:
      # Mount source code for hot reloading
      - .:/app
      # Exclude only node_modules for performance (vendor needed for dev deps)
      - /app/node_modules
      # Mount storage for file uploads and logs
      - ./storage:/app/storage
    depends_on:
      - mariadb
      - redis
    networks:
      - backend-network
    # Enable for debugging
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # MariaDB Database Service
  mariadb:
    image: mariadb:10.11
    container_name: backend-mariadb-dev
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: backend
      MYSQL_USER: backend_user
      MYSQL_PASSWORD: backend_password
    volumes:
      - mariadb_dev_data:/var/lib/mysql
      - ./database/mysql-init:/docker-entrypoint-initdb.d
    networks:
      - backend-network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis Cache Service
  redis:
    image: redis:7.2-alpine
    container_name: backend-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - backend-network

  # phpMyAdmin Service for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: backend-phpmyadmin-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: secret
      MYSQL_ROOT_PASSWORD: secret
    depends_on:
      - mariadb
    networks:
      - backend-network
volumes:
  mariadb_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  backend-network:
    driver: bridge
