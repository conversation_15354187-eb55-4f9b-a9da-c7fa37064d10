<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_prompts', function (Blueprint $table) {
            $table->id();
            $table->string('type')->unique(); // identifies the unit type this prompt applies to
            $table->longText('prompt'); // the AI prompt template containing placeholders
            $table->string('item_replace_pattern'); // template for formatting individual answers
            $table->string('default_context')->nullable(); // fallback context when assessment context is empty
            $table->timestamps();
            
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_prompts');
    }
};
