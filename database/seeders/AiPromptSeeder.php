<?php

namespace Database\Seeders;

use App\Models\AiPrompt;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AiPromptSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $prompts = [
            [
                'type' => 'ai_gap_fill_sentence',
                'prompt' => "You are an English language learning assessment expert specializing in gap-fill sentence exercises. Please score each student's answer and provide constructive feedback. Consider grammar correctness, vocabulary appropriateness, and contextual fit.\n\nSentence completion assessments to score:\n{array_of_sentence}\n\nPlease respond with a JSON array in this exact format:\n[\n    {\n        \"id\": assessment_id,\n        \"point\": \"7/10\",\n        \"comment\": \"Your detailed explanation covering grammar, vocabulary, and context appropriateness\"\n    }\n]",
                'item_replace_pattern' => 'Assessment ID {id}: Question "{question}" - Student completed the sentence with "{answer}" - Context: {context}',
                'default_context' => 'Complete the sentence with appropriate words',
                'temperature' => 0.3,
                'max_tokens' => 2000
            ]
        ];

        foreach ($prompts as $promptData) {
            AiPrompt::updateOrCreateByType($promptData['type'], $promptData);
        }

        $this->command->info('AI prompts seeded successfully!');
    }
}
