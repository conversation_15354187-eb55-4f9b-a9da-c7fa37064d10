<?php

namespace Database\Seeders;

use App\Models\Staff;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class StaffSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Staff::create([
        //     'username' => '<EMAIL>',
        //     'password' => Hash::make('password'),
        // ]);

        Staff::create([
            'username' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }
}
