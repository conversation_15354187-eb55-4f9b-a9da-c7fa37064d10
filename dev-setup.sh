#!/bin/bash

# Laravel Octane Development Environment Setup Script
set -e

echo "🚀 Setting up Laravel Octane Development Environment..."

# Check if Dock<PERSON> is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from development template..."
    cp .env.dev.example .env
    echo "✅ .env file created. Please update it with your specific configuration."
else
    echo "ℹ️  .env file already exists."
fi

# Build and start development containers
echo "🏗️  Building development containers..."
docker-compose -f docker-compose.dev.yml build --no-cache

echo "🚀 Starting development containers..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Generate application key if not set
if grep -q "APP_KEY=$" .env; then
    echo "🔑 Generating application key..."
    docker exec backend-api-dev php artisan key:generate
fi

# Run database migrations
echo "📊 Running database migrations..."
docker exec backend-api-dev php artisan migrate --force

# Run database seeding (if available)
if docker exec backend-api-dev php artisan db:seed --class=DatabaseSeeder --dry-run >/dev/null 2>&1; then
    echo "🌱 Running database seeders..."
    docker exec backend-api-dev php artisan db:seed --force
fi

# Install development dependencies that might be missing
echo "📦 Installing development dependencies..."
docker exec backend-api-dev composer require laravel/pail --dev --no-interaction

# Fix git ownership issues
echo "🔧 Fixing git ownership..."
docker exec backend-api-dev git config --global --add safe.directory /app

# Install Octane if not already installed
echo "⚡ Setting up Laravel Octane..."
docker exec backend-api-dev php artisan octane:install --server=frankenphp --no-interaction

# Clear and cache configuration
echo "🧹 Clearing and caching configuration..."
docker exec backend-api-dev php artisan config:clear
docker exec backend-api-dev php artisan config:cache
docker exec backend-api-dev php artisan route:cache

# Set proper permissions
echo "🔐 Setting proper permissions..."
docker exec backend-api-dev chown -R www-data:www-data /app/storage
docker exec backend-api-dev chmod -R 775 /app/storage
docker exec backend-api-dev chmod -R 775 /app/bootstrap/cache

echo ""
echo "✅ Development environment setup complete!"
echo ""
echo "🌐 Your Laravel application is now running at: http://localhost:8000"
echo "🗄️  phpMyAdmin is available at: http://localhost:8080"
echo "📧 MailHog (email testing) is available at: http://localhost:8025"
echo ""
echo "🛠️  Development commands:"
echo "  • Start containers: docker-compose -f docker-compose.dev.yml up -d"
echo "  • Stop containers: docker-compose -f docker-compose.dev.yml down"
echo "  • View logs: docker-compose -f docker-compose.dev.yml logs -f backend"
echo "  • Run artisan commands: docker exec backend-api-dev php artisan [command]"
echo "  • Access container shell: docker exec -it backend-api-dev bash"
echo "  • Reload Octane: docker exec backend-api-dev php artisan octane:reload"
echo ""
echo "🔧 Database connection details:"
echo "  • Host: localhost"
echo "  • Port: 3307"
echo "  • Database: backend"
echo "  • Username: root"
echo "  • Password: secret"
echo ""