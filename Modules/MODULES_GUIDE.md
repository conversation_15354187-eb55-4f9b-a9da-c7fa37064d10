# Laravel Modules Guide

This guide explains how to use the nwidart/laravel-modules package in this Laravel project.

## Overview

The nwidart/laravel-modules package allows you to organize your Laravel application into modular components. Each module is a self-contained package with its own controllers, models, views, routes, and other Laravel components.

## Installation Status

✅ **Package Installed**: nwidart/laravel-modules v12.0.4
✅ **Configuration Published**: config/modules.php
✅ **Autoloading Configured**: Composer merge-plugin setup
✅ **Example Module Created**: Blog module

## Project Configuration

### Composer Configuration
The main `composer.json` has been configured with the composer-merge-plugin to automatically include module autoloading:

```json
"extra": {
    "merge-plugin": {
        "include": [
            "Modules/*/composer.json"
        ]
    }
}
```

### Module Configuration
- **Modules Path**: `Modules/` (in project root)
- **Namespace**: `Modules`
- **Assets Path**: `public/modules`
- **Activator**: File-based (uses `modules_statuses.json`)

## Available Commands

### Module Management
```bash
# List all modules
php artisan module:list

# Create a new module
php artisan module:make ModuleName

# Enable a module
php artisan module:enable ModuleName

# Disable a module
php artisan module:disable ModuleName

# Delete a module
php artisan module:delete ModuleName
```

### Code Generation
```bash
# Generate controller
php artisan module:make-controller ControllerName ModuleName

# Generate model
php artisan module:make-model ModelName ModuleName

# Generate migration
php artisan module:make-migration create_table_name ModuleName

# Generate seeder
php artisan module:make-seeder SeederName ModuleName

# Generate request
php artisan module:make-request RequestName ModuleName

# Generate middleware
php artisan module:make-middleware MiddlewareName ModuleName

# Generate provider
php artisan module:make-provider ProviderName ModuleName

# Generate command
php artisan module:make-command CommandName ModuleName
```

## Module Structure

Each module follows this structure:
```
Modules/ModuleName/
├── app/
│   ├── Http/
│   │   └── Controllers/
│   ├── Models/
│   └── Providers/
├── config/
├── database/
│   ├── factories/
│   ├── migrations/
│   └── seeders/
├── resources/
│   ├── assets/
│   └── views/
├── routes/
│   ├── api.php
│   └── web.php
├── tests/
├── composer.json
├── module.json
└── package.json
```

## Example: Blog Module

A Blog module has been created as an example. It includes:

- **Controller**: `Modules\Blog\Http\Controllers\BlogController`
- **Routes**: Both web and API routes registered
- **Views**: Blade templates with module-specific layouts
- **Service Providers**: Auto-registered with Laravel

### Accessing the Blog Module
- Web routes: `/blogs` (requires auth)
- API routes: `/api/v1/blogs`

## Best Practices

### 1. Module Naming
- Use PascalCase for module names (e.g., `UserManagement`, `BlogSystem`)
- Keep names descriptive but concise

### 2. Namespace Organization
- Follow PSR-4 standards
- Use the module namespace: `Modules\ModuleName\`

### 3. Dependencies
- Each module has its own `composer.json`
- Add module-specific dependencies there
- Shared dependencies go in the main `composer.json`

### 4. Configuration
- Module-specific config files go in `Modules/ModuleName/config/`
- Use the module name as the config key

### 5. Database
- Module migrations are auto-discovered
- Use descriptive migration names with module prefix
- Seeders should be namespaced to the module

### 6. Views
- Use module view namespace: `modulename::view.name`
- Example: `blog::index` for `Modules/Blog/resources/views/index.blade.php`

### 7. Routes
- Web routes should use appropriate middleware
- API routes are automatically prefixed with `/api/v1/`
- Use route names with module prefix

## Development Workflow

### Creating a New Module
1. Generate the module:
   ```bash
   php artisan module:make YourModule
   ```

2. The module is automatically enabled and autoloaded

3. Develop your module components using the generator commands

4. Test your module functionality

### After Creating Components
Always run `composer dump-autoload` after creating new classes to ensure they're properly autoloaded.

## Troubleshooting

### Module Classes Not Found
If you encounter "Class not found" errors:
1. Run `composer dump-autoload`
2. Check that the module is enabled: `php artisan module:list`
3. Verify the module's `composer.json` autoloading configuration

### Routes Not Working
1. Check if the module is enabled
2. Verify route middleware requirements
3. Clear route cache: `php artisan route:clear`

### Views Not Loading
1. Check view namespace usage: `modulename::view.name`
2. Verify view files exist in the correct path
3. Clear view cache: `php artisan view:clear`

## Integration with Existing Code

Modules can interact with the main application:
- Use existing models and services
- Share middleware and policies
- Extend base controllers from the main app
- Use shared configuration and services

## Performance Considerations

- Only enable modules you're actively using
- Consider lazy loading for large modules
- Use module-specific caching strategies
- Monitor autoloading performance with many modules

## Next Steps

1. Explore the Blog module to understand the structure
2. Create your own modules for different features
3. Consider organizing related functionality into modules
4. Use modules for feature-based development

For more detailed information, refer to the [nwidart/laravel-modules documentation](https://nwidart.com/laravel-modules/).
