<?php

return [
    'name' => 'Storage',

    /*
    |--------------------------------------------------------------------------
    | Cloudflare R2 Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Cloudflare R2 (S3-compatible) storage service.
    | These settings are used to connect to your R2 bucket.
    |
    */
    'r2' => [
        'key' => env('R2_ACCESS_KEY_ID'),
        'secret' => env('R2_SECRET_ACCESS_KEY'),
        'region' => env('R2_DEFAULT_REGION', 'auto'),
        'bucket' => env('R2_BUCKET'),
        'endpoint' => env('R2_ENDPOINT'),
        'public_url' => env('R2_PUBLIC_URL'),
        'use_path_style_endpoint' => env('R2_USE_PATH_STYLE_ENDPOINT', true),
        'throw' => env('R2_THROW', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Presigned URL Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for generating presigned URLs for direct client uploads
    | and temporary file access.
    |
    */
    'presigned_urls' => [
        'upload_expiry' => env('R2_UPLOAD_EXPIRY', '+1 hour'),
        'download_expiry' => env('R2_DOWNLOAD_EXPIRY', '+15 minutes'),
        'max_file_size' => env('R2_MAX_FILE_SIZE', 10485760), // 10MB in bytes
    ],

    /*
    |--------------------------------------------------------------------------
    | File Validation
    |--------------------------------------------------------------------------
    |
    | Configuration for file validation and restrictions.
    |
    */
    'validation' => [
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'text/plain',
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'audio/mpeg',
            'audio/wav',
            'audio/x-m4a',
        ],
        'max_file_size' => env('R2_MAX_FILE_SIZE', 10485760), // 10MB in bytes
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Paths
    |--------------------------------------------------------------------------
    |
    | Default paths for organizing files in the R2 bucket.
    |
    */
    'paths' => [
        'uploads' => 'uploads',
        'temp' => 'temp',
        'public' => 'public',
        'private' => 'private',
    ],
];
