<?php

namespace Modules\Storage\Tests\Unit;

use Tests\TestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Modules\Storage\Services\StorageService;
use Modules\Storage\Events\FileUploadCompleted;
use Modules\Storage\Events\FileDeleted;
use Modules\Storage\Events\StorageError;
use Illuminate\Support\Facades\Event;
use Mockery;

class StorageServiceTest extends TestCase
{
    protected StorageService $storageService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the configuration
        config([
            'storage.r2' => [
                'key' => 'test-key',
                'secret' => 'test-secret',
                'region' => 'auto',
                'bucket' => 'test-bucket',
                'endpoint' => 'https://test.r2.cloudflarestorage.com',
                'use_path_style_endpoint' => true,
                'throw' => true,
            ],
            'storage.validation' => [
                'allowed_mime_types' => ['image/jpeg', 'image/png', 'text/plain'],
                'max_file_size' => 10485760, // 10MB
            ],
            'storage.paths' => [
                'uploads' => 'uploads',
                'temp' => 'temp',
                'public' => 'public',
                'private' => 'private',
            ],
        ]);
    }

    public function test_generate_unique_filename()
    {
        // Mock config method to avoid S3 client initialization
        $this->mock(StorageService::class, function ($mock) {
            $mock->shouldReceive('__construct')->andReturnNull();
            $mock->shouldReceive('generateUniqueFilename')
                ->with('jpg')
                ->andReturn('file_1234567890_abcdef1234567890.jpg');
        });

        $service = app(StorageService::class);
        $filename = $service->generateUniqueFilename('jpg');

        $this->assertStringStartsWith('file_', $filename);
        $this->assertStringEndsWith('.jpg', $filename);
    }

    public function test_generate_unique_filename_from_file()
    {
        $file = UploadedFile::fake()->create('test.jpg', 100);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass(StorageService::class);
        $method = $reflection->getMethod('generateUniqueFilenameFromFile');
        $method->setAccessible(true);

        $service = Mockery::mock(StorageService::class)->makePartial();
        $filename = $method->invoke($service, $file);

        $this->assertStringContainsString('test_', $filename);
        $this->assertStringEndsWith('.jpg', $filename);
    }

    public function test_build_file_path()
    {
        // Use reflection to access protected method
        $reflection = new \ReflectionClass(StorageService::class);
        $method = $reflection->getMethod('buildFilePath');
        $method->setAccessible(true);

        $service = Mockery::mock(StorageService::class)->makePartial();

        // Test with custom path
        $path = $method->invoke($service, 'custom/path', 'test.jpg');
        $this->assertEquals('custom/path/test.jpg', $path);

        // Test with null path (should use default)
        $path = $method->invoke($service, null, 'test.jpg');
        $this->assertEquals('uploads/test.jpg', $path);
    }

    public function test_validate_file_size()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('File size exceeds maximum allowed size');

        // Create a file larger than the configured limit
        $file = UploadedFile::fake()->create('large.jpg', 20480); // 20MB

        // Use reflection to access protected method
        $reflection = new \ReflectionClass(StorageService::class);
        $method = $reflection->getMethod('validateFile');
        $method->setAccessible(true);

        $service = Mockery::mock(StorageService::class)->makePartial();
        $method->invoke($service, $file);
    }

    public function test_validate_file_mime_type()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('File type not allowed');

        // Create a file with disallowed MIME type
        $file = UploadedFile::fake()->create('test.pdf', 100, 'application/pdf');

        // Use reflection to access protected method
        $reflection = new \ReflectionClass(StorageService::class);
        $method = $reflection->getMethod('validateFile');
        $method->setAccessible(true);

        $service = Mockery::mock(StorageService::class)->makePartial();
        $method->invoke($service, $file);
    }

        public function test_get_public_url()
    {
        // Mock service to avoid S3 client initialization
        $this->mock(StorageService::class, function ($mock) {
            $mock->shouldReceive('__construct')->andReturnNull();
            $mock->shouldReceive('getPublicUrl')
                ->with('uploads/test.jpg')
                ->andReturn('https://pub-974d830b0af14e40947fab4abc1ceaac.r2.dev/uploads/test.jpg');
        });

        $service = app(StorageService::class);
        $url = $service->getPublicUrl('uploads/test.jpg');

        $expected = 'https://pub-974d830b0af14e40947fab4abc1ceaac.r2.dev/uploads/test.jpg';
        $this->assertEquals($expected, $url);
    }

    public function test_presigned_temp_upload_url_structure()
    {
        // Mock the StorageService to avoid actual S3 calls
        $service = Mockery::mock(StorageService::class)->makePartial();

        // Mock the generateUniqueFilename method
        $service->shouldReceive('generateUniqueFilename')
                ->with('jpg')
                ->andReturn('file_1234567890_abcdef1234567890.jpg');

        // Mock the S3 client methods that would normally be called
        $service->shouldReceive('presignedTempUploadUrl')
                ->with('jpg')
                ->passthru();

        // Since we can't easily mock the internal S3 client without significant setup,
        // let's test the method exists and accepts the right parameters
        $reflection = new \ReflectionClass(StorageService::class);
        $method = $reflection->getMethod('presignedTempUploadUrl');

        // Check method exists and has correct parameters
        $this->assertTrue($method->isPublic());
        $this->assertEquals(3, $method->getNumberOfParameters()); // extension, path, expiresIn

        $parameters = $method->getParameters();
        $this->assertEquals('extension', $parameters[0]->getName());
        $this->assertEquals('path', $parameters[1]->getName());
        $this->assertEquals('expiresIn', $parameters[2]->getName());
        $this->assertTrue($parameters[1]->allowsNull()); // path is nullable
        $this->assertTrue($parameters[2]->isDefaultValueAvailable()); // expiresIn has default
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
