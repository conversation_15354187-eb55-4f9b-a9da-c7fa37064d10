<?php

namespace Modules\Storage\Tests\Feature;

use Tests\TestCase;
use Illuminate\Http\UploadedFile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Storage\Services\StorageService;
use Mockery;

class StorageApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the configuration
        config([
            'storage.r2' => [
                'key' => 'test-key',
                'secret' => 'test-secret',
                'region' => 'auto',
                'bucket' => 'test-bucket',
                'endpoint' => 'https://test.r2.cloudflarestorage.com',
                'use_path_style_endpoint' => true,
                'throw' => true,
            ],
            'storage.validation' => [
                'allowed_mime_types' => ['image/jpeg', 'image/png', 'text/plain'],
                'max_file_size' => 10485760, // 10MB
            ],
        ]);
    }

    public function test_upload_file_validation_required_file()
    {
        $response = $this->postJson('/api/v1/storage/upload', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_file_validation_file_size()
    {
        $file = UploadedFile::fake()->create('large.jpg', 20480); // 20MB

        $response = $this->postJson('/api/v1/storage/upload', [
            'file' => $file
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_presigned_upload_url_validation()
    {
        $response = $this->postJson('/api/v1/storage/presigned-upload-url', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['path', 'content_type']);
    }

    public function test_presigned_upload_url_invalid_content_type()
    {
        $response = $this->postJson('/api/v1/storage/presigned-upload-url', [
            'path' => 'test/file.exe',
            'content_type' => 'application/x-executable'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['content_type']);
    }

    public function test_presigned_upload_url_valid_request()
    {
        // Mock the StorageService
        $mockService = Mockery::mock(StorageService::class);
        $mockService->shouldReceive('generatePresignedUploadUrl')
                   ->once()
                   ->with('test/file.jpg', 'image/jpeg', 3600)
                   ->andReturn('https://presigned-url.example.com');

        $this->app->instance(StorageService::class, $mockService);

        $response = $this->postJson('/api/v1/storage/presigned-upload-url', [
            'path' => 'test/file.jpg',
            'content_type' => 'image/jpeg'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Presigned upload URL generated successfully',
                    'data' => [
                        'upload_url' => 'https://presigned-url.example.com',
                        'path' => 'test/file.jpg',
                        'expires_in' => 3600
                    ]
                ]);
    }

    public function test_delete_file_missing_path()
    {
        $response = $this->deleteJson('/api/v1/storage/delete', []);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'File path is required'
                ]);
    }

    public function test_presigned_temp_upload_url_validation()
    {
        $response = $this->postJson('/api/v1/storage/presigned-temp-upload-url', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['extension']);
    }

    public function test_presigned_temp_upload_url_valid_request()
    {
        // Mock the StorageService
        $mockService = Mockery::mock(StorageService::class);
        $mockService->shouldReceive('presignedTempUploadUrl')
                   ->once()
                   ->with('jpg', 1200)
                   ->andReturn([
                       'presigned_url' => 'https://presigned-url.example.com',
                       'public_url' => 'https://pub-974d830b0af14e40947fab4abc1ceaac.r2.dev/uploads/file_123456789_abcdef.jpg',
                       'file_path' => 'uploads/file_123456789_abcdef.jpg',
                       'filename' => 'file_123456789_abcdef.jpg'
                   ]);

        $this->app->instance(StorageService::class, $mockService);

        $response = $this->postJson('/api/v1/storage/presigned-temp-upload-url', [
            'extension' => 'jpg'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Presigned temporary upload URL generated successfully',
                    'data' => [
                        'presigned_url' => 'https://presigned-url.example.com',
                        'public_url' => 'https://pub-974d830b0af14e40947fab4abc1ceaac.r2.dev/uploads/file_123456789_abcdef.jpg',
                        'file_path' => 'uploads/file_123456789_abcdef.jpg',
                        'filename' => 'file_123456789_abcdef.jpg',
                        'expires_in' => 1200
                    ]
                ]);
    }

        public function test_presigned_temp_upload_url_different_extension()
    {
        // Mock the StorageService
        $mockService = Mockery::mock(StorageService::class);
        $mockService->shouldReceive('presignedTempUploadUrl')
                   ->once()
                   ->with('pdf', 1200)
                   ->andReturn([
                       'presigned_url' => 'https://presigned-url.example.com',
                       'public_url' => 'https://pub-974d830b0af14e40947fab4abc1ceaac.r2.dev/uploads/file_123456789_abcdef.pdf',
                       'file_path' => 'uploads/file_123456789_abcdef.pdf',
                       'filename' => 'file_123456789_abcdef.pdf'
                   ]);

        $this->app->instance(StorageService::class, $mockService);

        $response = $this->postJson('/api/v1/storage/presigned-temp-upload-url', [
            'extension' => 'pdf'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'presigned_url',
                        'public_url',
                        'file_path',
                        'filename',
                        'expires_in'
                    ]
                ]);
    }

    public function test_metadata_missing_path()
    {
        $response = $this->getJson('/api/v1/storage/metadata');

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'File path is required'
                ]);
    }

    public function test_exists_missing_path()
    {
        $response = $this->getJson('/api/v1/storage/exists');

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'File path is required'
                ]);
    }

    public function test_file_exists_check()
    {
        // Mock the StorageService
        $mockService = Mockery::mock(StorageService::class);
        $mockService->shouldReceive('fileExists')
                   ->once()
                   ->with('test/file.jpg')
                   ->andReturn(true);

        $this->app->instance(StorageService::class, $mockService);

        $response = $this->getJson('/api/v1/storage/exists?path=test/file.jpg');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'File existence checked successfully',
                    'data' => [
                        'exists' => true,
                        'path' => 'test/file.jpg'
                    ]
                ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
