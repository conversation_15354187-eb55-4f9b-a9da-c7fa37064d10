<?php

use Illuminate\Support\Facades\Route;
use Modules\Storage\Http\Controllers\StorageController;

Route::middleware(['admin.auth'])->prefix('v1')->name('storage.')->group(function () {
    Route::delete('storage/delete/{id}', [StorageController::class, 'delete'])->name('delete');
    Route::post('storage/presigned-upload-url', [StorageController::class, 'presignedUploadUrl'])->name('presigned.upload');
    Route::post('storage/presigned-download-url', [StorageController::class, 'presignedDownloadUrl'])->name('presigned.download');
    Route::post('storage/presigned-temp-upload-url', [StorageController::class, 'presignedTempUploadUrl'])->name('presigned.temp.upload');
});
