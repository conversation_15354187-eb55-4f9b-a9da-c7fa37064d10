# File Model Usage Guide

The File model in the Storage module provides a polymorphic relationship system that allows any model in your application to have file attachments.

## Model Overview

The `File` model is located at `Modules\Storage\Entities\File` and provides:
- Polymorphic relationships to attach files to any model
- Mass assignable attributes for file properties
- Automatic handling of `fileable_id` and `fileable_type` columns

## Database Schema

The `files` table contains:
- `id` - Primary key
- `filename` - Original filename
- `path` - File storage path
- `size` - File size in bytes
- `fileable_id` - ID of the related model
- `fileable_type` - Class name of the related model
- `created_at` & `updated_at` - Timestamps

## Basic Usage

### 1. Import the File Model

```php
use Modules\Storage\Entities\File;
```

### 2. Create a File Record

```php
$file = File::create([
    'filename' => 'document.pdf',
    'path' => '/storage/documents/document.pdf',
    'size' => 1024000,
    'fileable_id' => 1,
    'fileable_type' => 'App\\Models\\User',
]);
```

### 3. Access the Related Model

```php
$file = File::find(1);
$relatedModel = $file->fileable; // Returns the related model instance
```

## Example Usage in Another Module

Assume you have a Blog module with a Post model:

```php
namespace Modules\Blog\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Storage\Entities\File;

class Post extends Model
{
    protected $fillable = ['title', 'content'];

    /**
     * Define a polymorphic one-to-many relationship:
     * A Post can have multiple File attachments.
     */
    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }
}
```

### Attaching Files to a Post

```php
use Modules\Blog\Entities\Post;

$post = Post::find(1);

// Create and attach a file
$post->files()->create([
    'filename' => 'image.jpg',
    'path'     => '/storage/images/image.jpg',
    'size'     => 204800,
]);
```

When you run this, Laravel will automatically set:
- `fileable_id = 1`
- `fileable_type = 'Modules\Blog\Entities\Post'`

### Retrieving Files for a Post

```php
$post = Post::with('files')->find(1);

// Get all files for this post
$files = $post->files;

// Count files
$fileCount = $post->files()->count();

// Get files with specific conditions
$images = $post->files()->where('filename', 'like', '%.jpg')->get();
```

## Advanced Usage Examples

### Multiple Modules Using Files

#### User Profile Pictures
```php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Storage\Entities\File;

class User extends Model
{
    public function avatar()
    {
        return $this->morphOne(File::class, 'fileable');
    }
    
    public function documents()
    {
        return $this->morphMany(File::class, 'fileable');
    }
}
```

#### Course Module with Attachments
```php
namespace Modules\Course\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Storage\Entities\File;

class Course extends Model
{
    public function materials()
    {
        return $this->morphMany(File::class, 'fileable');
    }
    
    public function thumbnail()
    {
        return $this->morphOne(File::class, 'fileable')
                    ->where('filename', 'like', 'thumbnail%');
    }
}
```

### Querying Files Across Models

```php
// Find all files for a specific model type
$postFiles = File::where('fileable_type', 'Modules\\Blog\\Entities\\Post')->get();

// Find files by filename pattern
$imageFiles = File::where('filename', 'like', '%.jpg')
                  ->orWhere('filename', 'like', '%.png')
                  ->get();

// Find large files (>1MB)
$largeFiles = File::where('size', '>', 1048576)->get();
```

### Deleting Files with Cleanup

```php
$post = Post::find(1);

// Delete all files associated with the post
$post->files()->each(function ($file) {
    // Delete physical file
    Storage::delete($file->path);
    
    // Delete database record
    $file->delete();
});
```

## Best Practices

### 1. File Validation
Always validate files before creating records:

```php
$validated = $request->validate([
    'file' => 'required|file|max:10240', // 10MB max
]);

$uploadedFile = $request->file('file');
$path = $uploadedFile->store('uploads');

$post->files()->create([
    'filename' => $uploadedFile->getClientOriginalName(),
    'path' => $path,
    'size' => $uploadedFile->getSize(),
]);
```

### 2. File Type Categorization
Consider adding a `type` or `category` field:

```php
// Migration addition
$table->string('type')->nullable(); // 'image', 'document', 'video', etc.

// Model usage
$post->files()->create([
    'filename' => 'presentation.pdf',
    'path' => '/storage/documents/presentation.pdf',
    'size' => 2048000,
    'type' => 'document',
]);
```

### 3. Scoped Relationships
Create specific relationships for different file types:

```php
class Post extends Model
{
    public function images()
    {
        return $this->morphMany(File::class, 'fileable')
                    ->where('type', 'image');
    }
    
    public function documents()
    {
        return $this->morphMany(File::class, 'fileable')
                    ->where('type', 'document');
    }
}
```

### 4. File Cleanup Events
Use Laravel events to clean up physical files:

```php
// In your model's boot method
protected static function boot()
{
    parent::boot();
    
    static::deleting(function ($file) {
        Storage::delete($file->path);
    });
}
```

## Error Handling

```php
try {
    $post->files()->create([
        'filename' => $filename,
        'path' => $path,
        'size' => $size,
    ]);
} catch (\Exception $e) {
    // Handle file creation error
    Log::error('File creation failed: ' . $e->getMessage());
    
    // Clean up uploaded file if database insert fails
    Storage::delete($path);
}
```

## Performance Considerations

### Eager Loading
Always eager load files when needed:

```php
// Good
$posts = Post::with('files')->get();

// Bad - N+1 query problem
$posts = Post::all();
foreach ($posts as $post) {
    $files = $post->files; // Triggers additional query for each post
}
```

### Indexing
Consider adding database indexes for frequent queries:

```php
// In migration
$table->index(['fileable_type', 'fileable_id']);
$table->index('filename');
$table->index('size');
```

This polymorphic File model provides a flexible and reusable solution for file attachments across your entire application.