# Storage Module API

Base URL: `/api/v1/storage`  
Authentication: Bearer token required

## Delete File

**DELETE** `/api/v1/storage/delete`

**Payload:**
```json
{
  "path": "images/profile/avatar.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

## Generate Presigned Upload URL

**POST** `/api/v1/storage/presigned-upload-url`

**Payload:**
```json
{
  "path": "images/profile/avatar.jpg",
  "content_type": "image/jpeg",
  "expires_in": 3600
}
```

**Response:**
```json
{
  "success": true,
  "message": "Presigned upload URL generated successfully",
  "data": {
    "upload_url": "https://account.r2.cloudflarestorage.com/bucket/path?signature=...",
    "path": "images/profile/avatar.jpg",
    "expires_in": 3600
  }
}
```

## Generate Presigned Download URL

**POST** `/api/v1/storage/presigned-download-url`

**Payload:**
```json
{
  "path": "images/profile/avatar.jpg",
  "expires_in": 900
}
```

**Response:**
```json
{
  "success": true,
  "message": "Presigned download URL generated successfully",
  "data": {
    "download_url": "https://account.r2.cloudflarestorage.com/bucket/path?signature=...",
    "path": "images/profile/avatar.jpg",
    "expires_in": 900
  }
}
```
