<?php

namespace Modules\Storage\Helpers;

use Illuminate\Support\Facades\Log;

class StorageLogger
{
    /**
     * Log file upload operation
     */
    public static function logUpload(array $fileData, float $duration = null): void
    {
        $context = [
            'operation' => 'upload',
            'path' => $fileData['path'] ?? null,
            'filename' => $fileData['filename'] ?? null,
            'original_name' => $fileData['original_name'] ?? null,
            'size' => $fileData['size'] ?? null,
            'mime_type' => $fileData['mime_type'] ?? null,
        ];

        if ($duration !== null) {
            $context['duration_ms'] = round($duration * 1000, 2);
        }

        Log::info('File uploaded successfully', $context);
    }

    /**
     * Log file deletion operation
     */
    public static function logDeletion(string $path, float $duration = null): void
    {
        $context = [
            'operation' => 'delete',
            'path' => $path,
        ];

        if ($duration !== null) {
            $context['duration_ms'] = round($duration * 1000, 2);
        }

        Log::info('File deleted successfully', $context);
    }

    /**
     * Log presigned URL generation
     */
    public static function logPresignedUrl(string $type, string $path, int $expiresIn, float $duration = null): void
    {
        $context = [
            'operation' => 'presigned_url',
            'url_type' => $type,
            'path' => $path,
            'expires_in' => $expiresIn,
        ];

        if ($duration !== null) {
            $context['duration_ms'] = round($duration * 1000, 2);
        }

        Log::info("Presigned {$type} URL generated", $context);
    }

    /**
     * Log storage error
     */
    public static function logError(string $operation, string $message, array $context = [], ?\Throwable $exception = null): void
    {
        $logContext = array_merge([
            'operation' => $operation,
            'error_message' => $message,
        ], $context);

        if ($exception) {
            $logContext['exception'] = [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ];
        }

        Log::error("Storage operation failed: {$operation}", $logContext);
    }

    /**
     * Log file validation error
     */
    public static function logValidationError(string $filename, string $reason, array $context = []): void
    {
        $logContext = array_merge([
            'operation' => 'validation',
            'filename' => $filename,
            'validation_error' => $reason,
        ], $context);

        Log::warning('File validation failed', $logContext);
    }

    /**
     * Log configuration warning
     */
    public static function logConfigurationWarning(string $message, array $context = []): void
    {
        $logContext = array_merge([
            'operation' => 'configuration',
            'warning' => $message,
        ], $context);

        Log::warning('Storage configuration warning', $logContext);
    }

    /**
     * Log performance metrics
     */
    public static function logPerformance(string $operation, float $duration, array $context = []): void
    {
        $logContext = array_merge([
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'performance_log' => true,
        ], $context);

        // Log as warning if operation takes longer than 5 seconds
        if ($duration > 5.0) {
            Log::warning("Slow storage operation: {$operation}", $logContext);
        } else {
            Log::info("Storage operation completed: {$operation}", $logContext);
        }
    }

    /**
     * Log API request
     */
    public static function logApiRequest(string $endpoint, array $parameters = [], string $userAgent = null): void
    {
        $context = [
            'operation' => 'api_request',
            'endpoint' => $endpoint,
            'parameters' => $parameters,
        ];

        if ($userAgent) {
            $context['user_agent'] = $userAgent;
        }

        Log::info("Storage API request: {$endpoint}", $context);
    }

    /**
     * Log API response
     */
    public static function logApiResponse(string $endpoint, int $statusCode, float $duration = null): void
    {
        $context = [
            'operation' => 'api_response',
            'endpoint' => $endpoint,
            'status_code' => $statusCode,
        ];

        if ($duration !== null) {
            $context['duration_ms'] = round($duration * 1000, 2);
        }

        $level = $statusCode >= 400 ? 'error' : 'info';
        Log::$level("Storage API response: {$endpoint}", $context);
    }
}
