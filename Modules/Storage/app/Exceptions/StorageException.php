<?php

namespace Modules\Storage\Exceptions;

use Exception;

class StorageException extends Exception
{
    protected array $context;

    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get the exception context data
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set the exception context data
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * Create a file upload exception
     */
    public static function uploadFailed(string $filename, string $reason, ?Exception $previous = null): self
    {
        return new self(
            "File upload failed for '{$filename}': {$reason}",
            500,
            $previous,
            ['operation' => 'upload', 'filename' => $filename, 'reason' => $reason]
        );
    }

    /**
     * Create a file deletion exception
     */
    public static function deletionFailed(string $path, string $reason, ?Exception $previous = null): self
    {
        return new self(
            "File deletion failed for '{$path}': {$reason}",
            500,
            $previous,
            ['operation' => 'delete', 'path' => $path, 'reason' => $reason]
        );
    }

    /**
     * Create a presigned URL generation exception
     */
    public static function presignedUrlFailed(string $operation, string $path, string $reason, ?Exception $previous = null): self
    {
        return new self(
            "Presigned URL generation failed for {$operation} on '{$path}': {$reason}",
            500,
            $previous,
            ['operation' => 'presigned_url', 'url_type' => $operation, 'path' => $path, 'reason' => $reason]
        );
    }

    /**
     * Create a file validation exception
     */
    public static function validationFailed(string $filename, string $reason): self
    {
        return new self(
            "File validation failed for '{$filename}': {$reason}",
            400,
            null,
            ['operation' => 'validation', 'filename' => $filename, 'reason' => $reason]
        );
    }

    /**
     * Create a configuration exception
     */
    public static function configurationError(string $reason): self
    {
        return new self(
            "Storage configuration error: {$reason}",
            500,
            null,
            ['operation' => 'configuration', 'reason' => $reason]
        );
    }
}
