<?php

namespace Modules\Storage\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PresignedUrlRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $allowedMimeTypes = config('storage.validation.allowed_mime_types', []);

        return [
            'path' => 'required|string|max:255',
            'content_type' => [
                'required',
                'string',
                'in:' . implode(',', $allowedMimeTypes)
            ],
            'expires_in' => 'nullable|integer|min:60|max:86400' // 1 minute to 24 hours
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'path.required' => 'A file path is required.',
            'path.string' => 'The path must be a valid string.',
            'path.max' => 'The path must not exceed 255 characters.',
            'content_type.required' => 'Content type is required.',
            'content_type.string' => 'Content type must be a valid string.',
            'content_type.in' => 'The content type is not allowed.',
            'expires_in.integer' => 'Expiry time must be an integer.',
            'expires_in.min' => 'Expiry time must be at least 60 seconds.',
            'expires_in.max' => 'Expiry time must not exceed 24 hours (86400 seconds).',
        ];
    }
}
