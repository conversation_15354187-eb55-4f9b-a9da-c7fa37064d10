<?php

namespace Modules\Storage\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'path' => $this->getPublicUrl(),
            'type' => $this->type
        ];
    }

    /**
     * Get the full public URL for the file
     *
     * @return string
     */
    private function getPublicUrl(): string
    {
        $publicUrl = config('storage.r2.public_url');

        if (empty($publicUrl)) {
            return $this->path;
        }

        // Remove trailing slash from public URL
        $publicUrl = rtrim($publicUrl, '/');

        // Ensure path starts without slash to avoid double slashes
        $path = ltrim($this->path, '/');

        return $publicUrl . '/' . $path;
    }
}
