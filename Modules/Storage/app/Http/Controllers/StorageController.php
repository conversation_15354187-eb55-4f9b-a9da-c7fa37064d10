<?php

namespace Modules\Storage\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Storage\Http\Requests\PresignedUrlRequest;
use Modules\Storage\Services\StorageService;
use Modules\Storage\Models\File;

class StorageController extends Controller
{
    protected StorageService $storageService;

    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * Delete a file from R2 storage
     */
    public function delete(int $id): JsonResponse
    {
        try {
            $file = File::find($id);

            if (!$file) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            $result = $this->storageService->deleteFile($file->path);

            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate presigned URL for upload
     */
    public function presignedUploadUrl(PresignedUrlRequest $request): JsonResponse
    {
        try {
            $path = $request->input('path');
            $contentType = $request->input('content_type');
            $expiresIn = $request->input('expires_in', 3600);

            $url = $this->storageService->generatePresignedUploadUrl($path, $contentType, $expiresIn);

            return response()->json([
                'success' => true,
                'message' => 'Presigned upload URL generated successfully',
                'data' => [
                    'upload_url' => $url,
                    'path' => $path,
                    'expires_in' => $expiresIn
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate presigned upload URL',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate presigned URL for temporary upload with auto-generated filename
     */
    public function presignedTempUploadUrl(Request $request): JsonResponse
    {
        $request->validate([
            'extension' => 'required|string|max:10',
        ]);

        try {
            $extension = $request->input('extension');
            $expiresIn = 20 * 60;

            $result = $this->storageService->presignedTempUploadUrl($extension, $expiresIn);

            return response()->json([
                'success' => true,
                'message' => 'Presigned temporary upload URL generated successfully',
                'data' => [
                    'presigned_url' => $result['presigned_url'],
                    'public_url' => $result['public_url'],
                    'file_path' => $result['file_path'],
                    'filename' => $result['filename'],
                    'expires_in' => $expiresIn,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate presigned temporary upload URL',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate presigned URL for download
     */
    public function presignedDownloadUrl(Request $request): JsonResponse
    {
        try {
            $path = $request->input('path');
            $expiresIn = $request->input('expires_in', 900);

            if (!$path) {
                return response()->json([
                    'success' => false,
                    'message' => 'File path is required'
                ], 400);
            }

            $url = $this->storageService->generatePresignedDownloadUrl($path, $expiresIn);

            return response()->json([
                'success' => true,
                'message' => 'Presigned download URL generated successfully',
                'data' => [
                    'download_url' => $url,
                    'path' => $path,
                    'expires_in' => $expiresIn
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate presigned download URL',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
