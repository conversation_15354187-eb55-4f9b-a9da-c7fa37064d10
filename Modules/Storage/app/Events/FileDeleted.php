<?php

namespace Modules\Storage\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FileDeleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public array $fileData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $fileData)
    {
        $this->fileData = $fileData;
    }
}
