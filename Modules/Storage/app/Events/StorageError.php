<?php

namespace Modules\Storage\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StorageError
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public array $errorData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $errorData)
    {
        $this->errorData = $errorData;
    }
}
