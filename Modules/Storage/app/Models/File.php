<?php

namespace Modules\Storage\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Storage\Enums\FileType;

class File extends Model
{
    protected $fillable = [
        'filename',
        'path',
        'size',
        'type',
        'fileable_id',
        'fileable_type',
    ];

    protected $casts = [
        'type' => FileType::class,
    ];

    public function fileable()
    {
        return $this->morphTo();
    }
}
