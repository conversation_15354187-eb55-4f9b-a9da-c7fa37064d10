<?php

namespace Modules\Storage\Listeners;

use Modules\Storage\Events\FileDeleted;
use Illuminate\Support\Facades\Log;

class LogFileDeletion
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FileDeleted $event): void
    {
        Log::info('File deleted', [
            'path' => $event->fileData['path'],
        ]);
    }
}
