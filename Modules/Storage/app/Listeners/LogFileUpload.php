<?php

namespace Modules\Storage\Listeners;

use Modules\Storage\Events\FileUploadCompleted;
use Illuminate\Support\Facades\Log;

class LogFileUpload
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FileUploadCompleted $event): void
    {
        Log::info('File upload completed', [
            'path' => $event->fileData['path'],
            'filename' => $event->fileData['filename'],
            'original_name' => $event->fileData['original_name'],
            'size' => $event->fileData['size'],
            'mime_type' => $event->fileData['mime_type'],
        ]);
    }
}
