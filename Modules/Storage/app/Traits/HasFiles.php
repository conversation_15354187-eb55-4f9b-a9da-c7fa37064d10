<?php

namespace Modules\Storage\Traits;

use Modules\Storage\Models\File;
use Modules\Storage\Http\Resources\FileResource;

trait HasFiles
{
    /**
     * Initialize the trait for an instance
     */
    public function initializeHasFiles()
    {
        // Make sure the files attribute is appended and raw files relationship is hidden
        $this->append('files');
        $this->makeHidden('rawFiles');
    }

    /**
     * Get all files for this model (raw relationship)
     */
    public function rawFiles()
    {
        return $this->morphMany(File::class, 'fileable');
    }

    /**
     * Get formatted files with only id and public URL (accessor)
     */
    public function getFilesAttribute()
    {
        // If rawFiles relationship is already loaded, use it
        if ($this->relationLoaded('rawFiles')) {
            return FileResource::collection($this->getRelation('rawFiles'))->resolve();
        }
        
        // Otherwise load the relationship
        return FileResource::collection($this->rawFiles()->get())->resolve();
    }
}