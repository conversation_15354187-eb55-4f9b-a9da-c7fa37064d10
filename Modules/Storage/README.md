# Storage Module

The Storage module provides comprehensive file storage capabilities using Cloudflare R2 (S3-compatible) storage service. It includes file upload/download, presigned URLs, event-driven architecture, and robust error handling.

## Features

- **File Operations**: Upload, delete, and retrieve file metadata
- **Presigned URLs**: Generate secure URLs for direct client uploads and downloads
- **Event-Driven**: Comprehensive event system for file operations
- **Validation**: File type and size validation
- **Error Handling**: Structured error handling and logging
- **API Endpoints**: RESTful API for all storage operations

## Installation & Configuration

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
# Cloudflare R2 Configuration
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_BUCKET=your-bucket-name
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_DEFAULT_REGION=auto
R2_USE_PATH_STYLE_ENDPOINT=true
R2_THROW=true

# File Upload Limits
R2_MAX_FILE_SIZE=********  # 10MB in bytes
R2_UPLOAD_EXPIRY="+1 hour"
R2_DOWNLOAD_EXPIRY="+15 minutes"
```

### 2. Configuration

The module configuration is located in `config/config.php`. Key settings include:

- **R2 Connection**: Credentials and endpoint configuration
- **File Validation**: Allowed MIME types and file size limits
- **Presigned URLs**: Expiry times for upload/download URLs
- **Storage Paths**: Default paths for organizing files

### 3. Dependencies

The module requires:
- `league/flysystem-aws-s3-v3`: S3-compatible storage adapter
- Laravel 12.x
- PHP 8.2+

## Usage

### Service Layer

The `StorageService` provides the core functionality:

```php
use Modules\Storage\Services\StorageService;

$storageService = app(StorageService::class);

// Upload a file
$result = $storageService->uploadFile($uploadedFile, 'uploads/images');

// Delete a file
$storageService->deleteFile('uploads/images/file.jpg');

// Get file metadata
$metadata = $storageService->getFileMetadata('uploads/images/file.jpg');

// Generate presigned URLs
$uploadUrl = $storageService->generatePresignedUploadUrl('path/file.jpg', 'image/jpeg');
$downloadUrl = $storageService->generatePresignedDownloadUrl('path/file.jpg');
```

### API Endpoints

All endpoints require authentication (`auth:sanctum` middleware).

#### Upload File
```http
POST /api/v1/storage/upload
Content-Type: multipart/form-data

file: [file]
path: uploads/images (optional)
metadata: {"key": "value"} (optional)
```

#### Delete File
```http
DELETE /api/v1/storage/delete
Content-Type: application/json

{
  "path": "uploads/images/file.jpg"
}
```

#### Get File Metadata
```http
GET /api/v1/storage/metadata?path=uploads/images/file.jpg
```

#### Generate Presigned Upload URL
```http
POST /api/v1/storage/presigned-upload-url
Content-Type: application/json

{
  "path": "uploads/images/file.jpg",
  "content_type": "image/jpeg",
  "expires_in": 3600
}
```

#### Generate Presigned Download URL
```http
POST /api/v1/storage/presigned-download-url
Content-Type: application/json

{
  "path": "uploads/images/file.jpg",
  "expires_in": 900
}
```

#### Check File Existence
```http
GET /api/v1/storage/exists?path=uploads/images/file.jpg
```

## Events

The module dispatches the following events:

### FileUploadCompleted
Triggered when a file upload is completed successfully.

```php
use Modules\Storage\Events\FileUploadCompleted;

// Event data includes:
// - path: File path in storage
// - filename: Generated filename
// - original_name: Original filename
// - mime_type: File MIME type
// - size: File size in bytes
// - url: Public URL
// - metadata: Custom metadata
```

### FileDeleted
Triggered when a file is deleted.

```php
use Modules\Storage\Events\FileDeleted;

// Event data includes:
// - path: Deleted file path
```

### StorageError
Triggered when a storage operation fails.

```php
use Modules\Storage\Events\StorageError;

// Event data includes:
// - operation: Failed operation type
// - error: Error message
// - Additional context data
```

## Error Handling

The module includes comprehensive error handling:

- **Custom Exceptions**: `StorageException` with context data
- **Structured Logging**: Detailed logs for all operations
- **API Error Responses**: Consistent error response format
- **Validation**: File type and size validation

## Testing

Run the module tests:

```bash
# Unit tests
php artisan test Modules/Storage/tests/Unit

# Feature tests
php artisan test Modules/Storage/tests/Feature

# All module tests
php artisan test Modules/Storage/tests
```

## Security Considerations

- All API endpoints require authentication
- File type validation prevents malicious uploads
- File size limits prevent abuse
- Presigned URLs have configurable expiry times
- Structured logging for audit trails

## Performance

- Efficient file streaming for large uploads
- Configurable file size limits
- Performance logging for slow operations
- Event-driven architecture for scalability

## Troubleshooting

### Common Issues

1. **Configuration Errors**: Verify R2 credentials and endpoint
2. **File Upload Failures**: Check file size and MIME type restrictions
3. **Permission Issues**: Ensure R2 bucket permissions are correct
4. **Network Issues**: Verify R2 endpoint accessibility

### Logging

All operations are logged with structured data. Check Laravel logs for detailed error information.

## API Response Format

All API responses follow the standardized format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## Contributing

When contributing to the Storage module:

1. Follow the established coding standards
2. Add tests for new functionality
3. Update documentation
4. Ensure proper error handling
5. Add appropriate logging
