<?php

namespace Tests\Feature;

use App\Models\AiPrompt;
use App\Models\Assessment;
use App\Models\AssessmentAiGapFillSentence;
use App\Models\Unit;
use App\Services\AiPromptService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TestPromptTest extends TestCase
{
    use RefreshDatabase;

    public function test_ai_prompt_service_supports_question_parameter()
    {
        // Create test data
        $aiPrompt = AiPrompt::create([
            'type' => 'ai_gap_fill_sentence',
            'prompt' => 'Test prompt with {array_of_sentence}',
            'item_replace_pattern' => 'ID {id}: Question "{question}" - Answer "{answer}" - Context: {context}',
            'default_context' => 'Default context',
            'temperature' => 0.3,
            'max_tokens' => 2000
        ]);

        $service = new AiPromptService();

        $answers = [
            [
                'id' => 1,
                'answer' => 'test answer',
                'context' => 'test context',
                'question' => 'test question'
            ]
        ];

        $result = $service->applyPrompt($aiPrompt, $answers);

        $this->assertStringContainsString('Question "test question"', $result);
        $this->assertStringContainsString('Answer "test answer"', $result);
        $this->assertStringContainsString('Context: test context', $result);
    }

    public function test_ai_prompt_service_handles_missing_question()
    {
        // Create test data
        $aiPrompt = AiPrompt::create([
            'type' => 'ai_gap_fill_sentence',
            'prompt' => 'Test prompt with {array_of_sentence}',
            'item_replace_pattern' => 'ID {id}: Question "{question}" - Answer "{answer}" - Context: {context}',
            'default_context' => 'Default context',
            'temperature' => 0.3,
            'max_tokens' => 2000
        ]);

        $service = new AiPromptService();

        $answers = [
            [
                'id' => 1,
                'answer' => 'test answer',
                'context' => 'test context'
                // No question provided
            ]
        ];

        $result = $service->applyPrompt($aiPrompt, $answers);

        $this->assertStringContainsString('Question ""', $result); // Empty question
        $this->assertStringContainsString('Answer "test answer"', $result);
        $this->assertStringContainsString('Context: test context', $result);
    }

    public function test_ai_prompt_model_has_new_fields()
    {
        $aiPrompt = new AiPrompt([
            'type' => 'test_type',
            'prompt' => 'test prompt',
            'item_replace_pattern' => 'test pattern',
            'default_context' => 'test context',
            'temperature' => 0.5,
            'max_tokens' => 1500
        ]);

        $this->assertEquals(0.5, $aiPrompt->temperature);
        $this->assertEquals(1500, $aiPrompt->max_tokens);
        $this->assertTrue(in_array('temperature', $aiPrompt->getFillable()));
        $this->assertTrue(in_array('max_tokens', $aiPrompt->getFillable()));
    }
}
