{"permissions": {"allow": ["<PERSON><PERSON>(docker exec:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__serena__find_symbol", "mcp__serena__activate_project", "Bash(php:*)", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__replace_regex", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__get_current_config", "mcp__sequential-thinking__sequentialthinking", "Bash(ls:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(timeout:*)", "mcp__serena__onboarding", "mcp__serena__find_file", "mcp__serena__write_memory", "mcp__serena__search_for_pattern", "mcp__serena__replace_symbol_body", "mcp__serena__insert_after_symbol", "mcp__serena__read_memory", "mcp__serena__think_about_whether_you_are_done", "Bash(docker logs:*)", "<PERSON><PERSON>(composer install:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(docker cp:*)", "mcp__serena__find_referencing_symbols", "mcp__serena__think_about_collected_information"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["sequential-thinking", "Context7"]}