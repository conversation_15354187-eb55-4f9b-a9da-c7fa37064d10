<?php


use App\Http\Controllers\Api\StaffAuthController;
use App\Http\Controllers\AssessmentController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\Admin\AiPromptController;
use App\Http\Controllers\Api\AiScoringController;
use App\Http\Controllers\SettingsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// =============================================================================
// PUBLIC ROUTES - No authentication required
// =============================================================================

Route::prefix('public')->group(function () {
    // API Health Check
    Route::get('/health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now(),
            'service' => 'backend-api'
        ]);
    });

    // Public endpoints
    Route::get('/info', function () {
        return response()->json([
            'app' => config('app.name'),
            'version' => '1.0.0',
            'endpoints' => [
                'public' => '/api/public/*',
                'auth' => '/api/auth/* (user authentication)',
                'user' => '/api/user/* (requires user token)',
                'admin' => '/api/admin/* (requires admin credentials)',
                'staff' => '/api/staff/* (requires staff token)'
            ]
        ]);
    });
});

// =============================================================================
// AUTHENTICATION ROUTES - Token-based authentication
// =============================================================================

Route::prefix('auth')->group(function () {
    // User authentication (no password required)

});

// =============================================================================
// USER ROUTES - User management (legacy support)
// =============================================================================

// =============================================================================
// USER ROUTES - Protected user endpoints
// =============================================================================

Route::prefix('user')->middleware('user.auth')->group(function () {

});

// =============================================================================
// ADMIN ROUTES - Public admin endpoints (no auth required)
// =============================================================================

Route::prefix('admin')->group(function () {
    // Public admin endpoints (no authentication required)
    Route::post('/login', [StaffAuthController::class, 'login']);

    // Protected admin routes (authentication required)
    Route::middleware('admin.auth')->group(function () {
        Route::post('/logout', [StaffAuthController::class, 'logout']);
        Route::get('/me', [StaffAuthController::class, 'me']);

        // Course management
        Route::apiResource('courses', CourseController::class)
            ->names('admin.courses');

        // Additional course management endpoints
        Route::prefix('courses/{course}')->group(function () {
            Route::post('/duplicate', [CourseController::class, 'duplicate'])
                ->name('admin.courses.duplicate');
        });

        // Course statistics endpoint
        Route::get('/courses-with-stats', [CourseController::class, 'withStats'])
            ->name('admin.courses.with-stats');

        // Unit management
        Route::apiResource('units', UnitController::class)
            ->names('admin.units');

        // Additional unit management endpoints
        Route::prefix('units/{unit}')->group(function () {
            Route::post('/duplicate', [UnitController::class, 'duplicate'])
                ->name('admin.units.duplicate');
            Route::patch('/move-to-course', [UnitController::class, 'moveToCourse'])
                ->name('admin.units.move-to-course');
            Route::patch('/reorder', [UnitController::class, 'reorder'])
                ->name('admin.units.reorder');
            Route::delete('/assessments', [UnitController::class, 'clearAssessments'])
                ->name('admin.units.assessments.clear');
        });

        // Course-specific unit endpoints
        Route::get('/courses/{courseId}/units', [UnitController::class, 'getByCourse'])
            ->name('admin.courses.units');

        // Unit-specific import functionality
        Route::prefix('units/{unit}')->group(function () {
            Route::post('/import/xlsx', [ImportController::class, 'importXlsx'])
                ->name('admin.units.import.xlsx');
        });

        // Global import functionality
        Route::prefix('import')->group(function () {
            Route::get('/template/{type}', [ImportController::class, 'downloadTemplate'])
                ->name('admin.import.template');
        });

        // Assessment management with unified controller
        Route::prefix('assessments')->group(function () {
            // Create assessment (type specified in request body)
            Route::post('/', [AssessmentController::class, 'store'])
                ->name('admin.assessments.store');

            Route::post('/attach-file/{id}', [AssessmentController::class, 'attachFile'])
                ->name('admin.assessments.attach-file');
            Route::delete('/detach-file/{id}', [AssessmentController::class, 'detachFile'])
                ->name('admin.assessments.detach-file');

            // Update assessment by type and ID
            Route::put('{type}/{id}', [AssessmentController::class, 'update'])
                ->name('admin.assessments.update');

            // Delete assessment by type and ID
            Route::delete('{type}/{id}', [AssessmentController::class, 'destroy'])
                ->name('admin.assessments.destroy');

            // Attach/detach units
            Route::post('{type}/{id}/attach-units', [AssessmentController::class, 'attachToUnits'])
                ->name('admin.assessments.attach-units');
            Route::post('{type}/{id}/detach-units', [AssessmentController::class, 'detachFromUnits'])
                ->name('admin.assessments.detach-units');
        });

        // AI Prompt management
        Route::prefix('ai-prompts')->group(function () {
            Route::get('/', [AiPromptController::class, 'index'])
                ->name('admin.ai-prompts.index');
            Route::put('{type}', [AiPromptController::class, 'update'])
                ->name('admin.ai-prompts.update');
            Route::post('/test', [AiScoringController::class, 'testPrompt'])
                ->name('admin.ai-prompts.test');
            Route::get('/assessments/{type}', [AiScoringController::class, 'listAiAssessment'])
                ->name('admin.ai-prompts.list-assessments');
        });

        Route::prefix('settings')->group(function () {
            Route::get('/', [SettingsController::class, 'adminSettings'])
                ->name('admin.settings.index');
        });
    });
});

// =============================================================================
// PUBLIC ASSESSMENT ROUTES - For quiz taking (no auth required for now)
// =============================================================================

Route::prefix('public')->group(function () {
    // Course viewing endpoints (read-only)
    Route::get('/courses', [CourseController::class, 'publicList'])
        ->name('public.courses.index');
    Route::get('/courses/{course}', [CourseController::class, 'showPublic'])
        ->name('public.courses.show');

    // Unit viewing endpoints (read-only)
    Route::get('/units/{unit}', [UnitController::class, 'showPublic'])
        ->name('public.units.show');
    Route::get('/courses/{courseId}/units', [UnitController::class, 'getByCoursePublic'])
        ->name('public.courses.units');

    Route::post('/assessments/{type}/score', [AssessmentController::class, 'score'])
        ->name('public.assessments.score');
});
