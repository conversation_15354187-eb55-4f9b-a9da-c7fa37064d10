<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Assessment;

class TestAssessmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:assessment {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Assessment polymorphic relationship';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');
        
        if ($id) {
            // Test specific assessment
            $this->testSpecificAssessment($id);
        } else {
            // Test first few assessments
            $this->testMultipleAssessments();
        }
    }

    private function testSpecificAssessment($id)
    {
        $this->info("Testing Assessment ID: {$id}");
        $this->line('----------------------------------------');

        try {
            // Your original code
            $assessment = Assessment::findOrFail($id);
            $assessmentDetail = $assessment->itemable;

            $this->info('✅ Assessment found!');
            $this->line("Base Assessment Data:");
            $this->line("  ID: {$assessment->id}");
            $this->line("  Skill Type: {$assessment->skill_type}");
            $this->line("  Difficulty: {$assessment->difficulty}");
            $this->line("  Itemable Type: {$assessment->itemable_type}");
            $this->line("  Itemable ID: {$assessment->itemable_id}");
            
            $this->line('');
            $this->line("Polymorphic Assessment Detail:");
            if ($assessmentDetail) {
                $this->line("  Class: " . get_class($assessmentDetail));
                $this->line("  ID: {$assessmentDetail->id}");
                
                // Show specific fields based on type
                if (isset($assessmentDetail->question)) {
                    $this->line("  Question: " . substr($assessmentDetail->question, 0, 100) . '...');
                }
                if (isset($assessmentDetail->answer_list)) {
                    $this->line("  Answer List: " . json_encode($assessmentDetail->answer_list));
                }
                if (isset($assessmentDetail->correct_answer_indexes)) {
                    $this->line("  Correct Answers: " . json_encode($assessmentDetail->correct_answer_indexes));
                }
            } else {
                $this->error("❌ No itemable relationship found!");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
        }
    }

    private function testMultipleAssessments()
    {
        $this->info("Testing multiple assessments...");
        $this->line('----------------------------------------');

        $assessments = Assessment::with('itemable')->take(5)->get();
        
        if ($assessments->count() === 0) {
            $this->error("❌ No assessments found in database!");
            return;
        }

        foreach ($assessments as $assessment) {
            $this->line("Assessment ID: {$assessment->id}");
            $this->line("  Type: {$assessment->itemable_type}");
            $this->line("  Detail Class: " . ($assessment->itemable ? get_class($assessment->itemable) : 'NULL'));
            $this->line('');
        }

        $this->info("✅ Found {$assessments->count()} assessments");
        $this->line("Run 'php artisan test:assessment <id>' to test a specific one");
    }
}
