<?php

namespace App\Contracts;

use Illuminate\Database\Eloquent\Model;

/**
 * Assessment Service Interface
 * 
 * Defines the contract for all assessment type services.
 * All assessment services must implement these core operations:
 * - CRUD operations (create, update, delete)
 * - Scoring functionality
 * - Unit relationship management
 */
interface AssessmentServiceInterface
{
    /**
     * Create a new assessment with its corresponding polymorphic Assessment record
     *
     * @param array $data Validated assessment data
     * @return Model The created assessment model with loaded relationships
     * @throws \Exception When creation fails
     */
    public function create(array $data): Model;

    /**
     * Update an existing assessment
     *
     * @param Model $assessment The assessment model to update
     * @param array $data Validated update data
     * @return Model The updated assessment model
     * @throws \Exception When update fails
     */
    public function update(Model $assessment, array $data): Model;

    /**
     * Delete an assessment and its corresponding Assessment record
     *
     * @param Model $assessment The assessment model to delete
     * @return bool True if deletion was successful
     * @throws \Exception When deletion fails
     */
    public function delete(Model $assessment): bool;

    /**
     * Score user's answer for this assessment type
     *
     * @param Model $assessment The assessment model
     * @param array $userAnswer User's submitted answer(s)
     * @return array Scoring result with standardized format
     * @throws \InvalidArgumentException When answer format is invalid
     * @throws \Exception When scoring fails
     */
    public function score(Model $assessment, array $userAnswer): array;

    /**
     * Attach assessment to units with optional ordering
     *
     * @param Model $assessment The assessment model
     * @param array $unitAttachments Array of unit attachments with unit_id and optional assessment_order
     * @return void
     * @throws \Exception When attachment fails
     */
    public function attachToUnits(Model $assessment, array $unitAttachments): void;

    /**
     * Detach assessment from units
     *
     * @param Model $assessment The assessment model
     * @param array $unitIds Optional array of unit IDs to detach from (empty = detach from all)
     * @return void
     * @throws \Exception When detachment fails
     */
    public function detachFromUnits(Model $assessment, array $unitIds = []): void;
}