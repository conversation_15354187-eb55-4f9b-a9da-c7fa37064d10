<?php

namespace App\Enums;

enum UnitType: string
{
    case AI_GAP_FILL_SENTENCE = 'ai_gap_fill_sentence';
    case MULTIPLE_SELECT = 'multiple_select';
    case GAP_FILL = 'gap_fill';
    case ANSWER_THE_QUESTION = 'answer_the_question';

    /**
     * Get all available unit types with descriptions
     */
    public static function getTypesWithDescriptions(): array
    {
        return [
            self::AI_GAP_FILL_SENTENCE->value => 'AI Gap Fill Sentence',
            self::MULTIPLE_SELECT->value => 'Multiple Select',
            self::GAP_FILL->value => 'Gap Fill',
            self::ANSWER_THE_QUESTION->value => 'Answer The Question',
        ];
    }

    /**
     * Get the corresponding assessment model class for this unit type
     */
    public static function getAssessmentModelClass(string $value): string
    {
        $unitType = self::from($value);

        return match ($unitType) {
            self::AI_GAP_FILL_SENTENCE => \App\Models\AssessmentAiGapFillSentence::class,
            self::MULTIPLE_SELECT => \App\Models\AssessmentMultipleSelect::class,
            self::GAP_FILL => \App\Models\AssessmentGapFill::class,
            self::ANSWER_THE_QUESTION => \App\Models\AssessmentAnswerTheQuestion::class,
        };
    }

    /**
     * Get the unit type from an assessment model class
     */
    public static function fromAssessmentModelClass(string $modelClass): self
    {
        return match ($modelClass) {
            \App\Models\AssessmentAiGapFillSentence::class => self::AI_GAP_FILL_SENTENCE,
            \App\Models\AssessmentMultipleSelect::class => self::MULTIPLE_SELECT,
            \App\Models\AssessmentGapFill::class => self::GAP_FILL,
            \App\Models\AssessmentAnswerTheQuestion::class => self::ANSWER_THE_QUESTION,
            default => throw new \InvalidArgumentException("Unknown assessment model class: {$modelClass}"),
        };
    }

    public static function getAiAssessmentType(): array
    {
        return [
            self::AI_GAP_FILL_SENTENCE->value,
        ];
    }

    public static function getSettings(): array
    {
        return [
            self::MULTIPLE_SELECT->value => [
                'name' => 'Multiple Select',
                'value' => self::MULTIPLE_SELECT->value,
            ],
            self::AI_GAP_FILL_SENTENCE->value => [
                'name' => 'AI Gap Fill Sentence',
                'value' => self::AI_GAP_FILL_SENTENCE->value,
            ],
            self::GAP_FILL->value => [
                'name' => 'Gap Fill',
                'value' => self::GAP_FILL->value,
            ],
            self::ANSWER_THE_QUESTION->value => [
                'name' => 'Answer The Question',
                'value' => self::ANSWER_THE_QUESTION->value,
            ],
        ];
    }

    public static function getAISettings(): array
    {
        return [
            self::AI_GAP_FILL_SENTENCE->value => [
                'name' => 'AI Gap Fill Sentence',
                'value' => self::AI_GAP_FILL_SENTENCE->value,
            ],
        ];
    }
}
