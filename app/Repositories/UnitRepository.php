<?php

namespace App\Repositories;

use App\Models\Unit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class UnitRepository
{
    /**
     * Get units with optional filtering and pagination
     */
    public function getUnitsWithFilters(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->buildFilteredQuery($filters)
            ->with(['course', 'assessments'])
            ->orderBy('course_id')
            ->orderBy('unit_order')
            ->paginate(min($perPage, 100));
    }

    /**
     * Get units by course with filtering
     */
    public function getUnitsByCourse(int $courseId, array $filters = [], int $perPage = 50): LengthAwarePaginator
    {
        $query = Unit::with(['assessments'])
            ->where('course_id', $courseId);

        // Apply additional filters
        if (!empty($filters['skill_type'])) {
            $query->where('skill_type', $filters['skill_type']);
        }

        if (!empty($filters['difficulty'])) {
            $query->where('difficulty', $filters['difficulty']);
        }

        return $query->orderBy('unit_order')
            ->paginate(min($perPage, 100));
    }

    /**
     * Get public units by course (only units with assessments)
     */
    public function getPublicUnitsByCourse(int $courseId, int $perPage = 50): LengthAwarePaginator
    {
        return Unit::with(['assessments'])
            ->where('course_id', $courseId)
            ->whereHas('assessments')
            ->orderBy('unit_order')
            ->paginate(min($perPage, 100));
    }

    /**
     * Get units with direct assessments for efficient loading
     */
    public function getUnitsWithDirectAssessments(array $unitIds): Collection
    {
        return Unit::with(['assessments.itemable'])
            ->whereIn('id', $unitIds)
            ->get();
    }

    /**
     * Find unit with full relationships loaded
     */
    public function findWithRelations(int $id): ?Unit
    {
        return Unit::with(['course', 'assessments.itemable'])->find($id);
    }

    /**
     * Get next unit order for a course
     */
    public function getNextUnitOrder(int $courseId): int
    {
        return Unit::where('course_id', $courseId)->max('unit_order') + 1;
    }

    /**
     * Get units in order range for reordering operations
     */
    public function getUnitsInOrderRange(int $courseId, int $startOrder, int $endOrder): Collection
    {
        return Unit::where('course_id', $courseId)
            ->whereBetween('unit_order', [$startOrder, $endOrder])
            ->orderBy('unit_order')
            ->get();
    }

    /**
     * Bulk update unit orders
     */
    public function bulkUpdateOrders(array $updates): void
    {
        foreach ($updates as $update) {
            Unit::where('id', $update['id'])
                ->update(['unit_order' => $update['order']]);
        }
    }

    /**
     * Count units by course and filters
     */
    public function countUnits(array $filters = []): int
    {
        return $this->buildFilteredQuery($filters)->count();
    }

    /**
     * Build filtered query
     */
    private function buildFilteredQuery(array $filters): Builder
    {
        $query = Unit::query();

        if (!empty($filters['course_id'])) {
            $query->where('course_id', $filters['course_id']);
        }

        if (!empty($filters['skill_type'])) {
            $query->where('skill_type', $filters['skill_type']);
        }

        if (!empty($filters['difficulty'])) {
            $query->where('difficulty', $filters['difficulty']);
        }

        return $query;
    }
}