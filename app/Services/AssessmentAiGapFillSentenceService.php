<?php

namespace App\Services;

use App\Models\AssessmentAiGapFillSentence;
use Illuminate\Database\Eloquent\Model;

class AssessmentAiGapFillSentenceService extends AssessmentService
{
    /**
     * Create a new AssessmentAiGapFillSentence with its corresponding Assessment
     */
    public function create(array $data): AssessmentAiGapFillSentence
    {
        // Create the AssessmentAiGapFillSentence first
        // fill_position will be auto-calculated in the model's boot method
        $assessmentAiGapFillSentence = AssessmentAiGapFillSentence::create([
            'question' => $data['question'],
            'context' => $data['context'],
        ]);

        // Create the polymorphic Assessment record and return with relationship loaded
        return $this->createAssessmentRecord($assessmentAiGapFillSentence);
    }

    /**
     * Update an existing AssessmentAiGapFillSentence
     */
    public function update(Model $assessment, array $data): Model
    {
        // fill_position will be auto-calculated in the model's boot method if question changes
        $assessment->update([
            'question' => $data['question'] ?? $assessment->question,
            'context' => $data['context'] ?? $assessment->context,
        ]);

        return $assessment->fresh();
    }

    /**
     * Score the user's answer for an AI gap fill assessment
     * Uses the standardized AiPromptService approach for consistency
     *
     * @param AssessmentAiGapFillSentence $assessment
     * @param array $userAnswer - Array of user's answers for each gap
     * @return array
     */
    public function score(Model $assessment, array $userAnswer): array
    {
        $fillPositions = $assessment->fill_position ?? [];
        $gapCount = count($fillPositions);
        $userAnswerCount = count($userAnswer);

        if ($userAnswerCount !== $gapCount) {
            return [
                'point' => '0',
                'comment' => 'Number of provided answers does not match the number of gaps',
            ];
        }

        // Get the assessment's unit to use AiPromptService approach
        $assessmentRecord = $assessment->assessment;
        if (!$assessmentRecord || !$assessmentRecord->units->first()) {
            return [
                'point' => '0',
                'comment' => 'Assessment not properly associated with a unit',
            ];
        }

        $unit = $assessmentRecord->units->first();

        // Format answer for AiPromptService (following your established pattern)
        $answersForAi = [[
            'assessment_id' => $assessmentRecord->id,
            'answer' => $this->buildCompletedSentence($assessment->question, $userAnswer, $fillPositions)
        ]];

        // Use your established AiPromptService approach
        $aiPromptService = app(AiPromptService::class);
        $result = $aiPromptService->getResult($unit->id, $answersForAi);

        // Extract the result for this specific assessment
        $aiOutput = $result['ai_output'];
        $assessmentResult = collect($aiOutput)->firstWhere('id', $assessmentRecord->id);

        if (!$assessmentResult) {
            throw new \Exception('AI service did not return result for this assessment');
        }

        return [
            'pass' => (float) explode('/', $assessmentResult['point'])[0] >= 8,
            'explain' => $assessmentResult['comment'],
        ];
    }

    /**
     * Build the completed sentence by replacing gaps with user answers
     */
    private function buildCompletedSentence(string $question, array $userAnswers, array $fillPositions): string
    {
        $sentence = $question;
        $offset = 0;

        foreach ($fillPositions as $index => $position) {
            $userAnswer = $userAnswers[$index] ?? '';

            // Find the gap (underscores) at this position
            $adjustedPosition = $position + $offset;
            $gapPattern = '/_{1,}/';

            if (preg_match($gapPattern, $sentence, $matches, PREG_OFFSET_CAPTURE, $adjustedPosition)) {
                $gapStart = $matches[0][1];
                $gapLength = strlen($matches[0][0]);

                // Replace the gap with user answer
                $sentence = substr_replace($sentence, $userAnswer, $gapStart, $gapLength);

                // Update offset for next replacements
                $offset += strlen($userAnswer) - $gapLength;
            }
        }

        return $sentence;
    }

}
