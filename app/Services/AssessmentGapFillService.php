<?php

namespace App\Services;

use App\Models\AssessmentGapFill;
use Illuminate\Database\Eloquent\Model;

class AssessmentGapFillService extends AssessmentService
{
    /**
     * Create a new AssessmentGapFill with its corresponding Assessment
     */
    public function create(array $data): AssessmentGapFill
    {
        // Create the AssessmentGapFill first
        $assessmentGapFill = AssessmentGapFill::create([
            'question' => $data['question'],
            'explanation' => $data['explanation'] ?? null,
            'correct_answers' => $data['correct_answers'],
        ]);

        // Create the polymorphic Assessment record and load relationship
        return $this->createAssessmentRecord($assessmentGapFill);
    }

    /**
     * Update an existing AssessmentGapFill
     */
    public function update(Model $assessment, array $data): Model
    {
        $assessment->update([
            'question' => $data['question'] ?? $assessment->question,
            'explanation' => $data['explanation'] ?? $assessment->explanation,
            'correct_answers' => $data['correct_answers'] ?? $assessment->correct_answers,
        ]);

        return $assessment->fresh();
    }

    /**
     * Score the user's answer for a gap fill assessment
     *
     * @param AssessmentGapFill $assessment
     * @param array $userAnswer - Array of user's answers for each gap
     * @return array
     */
    public function score(Model $assessment, array $userAnswer): array
    {
        $correctAnswers = $assessment->correct_answers;
        $explanation = $assessment->explanation ?? '';

        // Ensure we have the same number of answers as gaps
        $gapCount = count($correctAnswers);
        $userAnswerCount = count($userAnswer);

        if ($userAnswerCount !== $gapCount) {
            return [
                'explain' => 'Number of provided answers does not match the number of gaps',
                'pass' => false,
                'meta' => json_encode([
                    'expected_gaps' => $gapCount,
                    'provided_answers' => $userAnswerCount,
                    'assessment_type' => 'gap_fill'
                ])
            ];
        }

        $correctCount = 0;
        $incorrectAnswers = [];

        // Check each gap
        for ($i = 0; $i < $gapCount; $i++) {
            $userAnswerText = trim(strtolower($userAnswer[$i] ?? ''));

            // Split correct answers by "|" and clean them up
            $correctAnswerOptions = array_map('trim', array_map('strtolower', explode('|', $correctAnswers[$i])));

            // Remove empty options that might result from splitting
            $correctAnswerOptions = array_filter($correctAnswerOptions, function($option) {
                return !empty($option);
            });

            // Check if user answer matches any of the correct options for this gap
            if (in_array($userAnswerText, $correctAnswerOptions)) {
                $correctCount++;
            } else {
                $incorrectAnswers[] = [
                    'gap_index' => $i,
                    'user_answer' => $userAnswer[$i] ?? '',
                    'correct_options' => explode('|', $correctAnswers[$i])
                ];
            }
        }

        $isCorrect = $correctCount === $gapCount;

        // Generate explanation if not provided
        if (empty($explanation)) {
            if ($isCorrect) {
                $explanation = 'All gaps filled correctly!';
            } else {
                $explanation = sprintf(
                    'Correct: %d/%d gaps. Incorrect answers: %s',
                    $correctCount,
                    $gapCount,
                    json_encode($incorrectAnswers)
                );
            }
        }

        return [
            'explain' => $explanation,
            'pass' => $isCorrect,
        ];
    }
}
