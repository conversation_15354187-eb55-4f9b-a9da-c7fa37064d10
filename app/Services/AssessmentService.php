<?php

namespace App\Services;

use App\Models\Assessment;
use App\Contracts\AssessmentServiceInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

abstract class AssessmentService implements AssessmentServiceInterface
{
    /**
     * Create the polymorphic Assessment record and load the relationship
     */
    protected function createAssessmentRecord(Model $assessmentModel): Model
    {
        Assessment::create([
            'itemable_type' => get_class($assessmentModel),
            'itemable_id' => $assessmentModel->id,
        ]);

        return $assessmentModel->load('assessment');
    }

    /**
     * Delete an assessment and its corresponding Assessment record
     */
    public function delete(Model $assessmentModel): bool
    {
        try {
            DB::beginTransaction();

            $assessment = Assessment::where('itemable_type', get_class($assessmentModel))
                ->where('itemable_id', $assessmentModel->id)
                ->first();

            if ($assessment) {
                $assessment->units()->detach();
                $assessment->delete();
            }

            $result = $assessmentModel->delete();

            DB::commit();

            return $result === true || $result === 1;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Attach assessment to units with order
     */
    public function attachToUnits(Model $assessmentModel, array $unitAttachments): void
    {
        $assessment = Assessment::where('itemable_type', get_class($assessmentModel))
            ->where('itemable_id', $assessmentModel->id)
            ->firstOrFail();

        foreach ($unitAttachments as $attachment) {
            $unitId = $attachment['unit_id'];

            // If assessment_order is not provided, get the next available order
            if (!isset($attachment['assessment_order'])) {
                // Query the pivot table directly to get the max order for this unit
                $maxOrder = DB::table('unit_assessments')
                    ->where('unit_id', $unitId)
                    ->max('assessment_order');

                $assessmentOrder = ($maxOrder ?? 0) + 1;
            } else {
                $assessmentOrder = $attachment['assessment_order'];
            }

            $assessment->units()->attach($unitId, [
                'assessment_order' => $assessmentOrder
            ]);
        }
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(Model $assessmentModel, array $unitIds = []): void
    {
        $assessment = Assessment::where('itemable_type', get_class($assessmentModel))
            ->where('itemable_id', $assessmentModel->id)
            ->firstOrFail();

        if (empty($unitIds)) {
            $assessment->units()->detach();
        } else {
            $assessment->units()->detach($unitIds);
        }
    }

    /**
     * Abstract methods that must be implemented by concrete assessment services
     */
    abstract public function create(array $data): Model;
    abstract public function update(Model $assessment, array $data): Model;
    abstract public function score(Model $assessment, array $userAnswer): array;
}