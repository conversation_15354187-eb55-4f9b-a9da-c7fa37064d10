<?php

namespace App\Services;

use App\Models\AiPrompt;
use App\Models\Assessment;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use InvalidArgumentException;
use Illuminate\Support\Facades\Log;
use App\Models\Unit;

class AiPromptService
{
    /**
     * Retrieve prompt configuration by unit type
     *
     * @param string $type The unit type
     * @return AiPrompt
     * @throws ModelNotFoundException if type not found
     */
    public function getPromptByType(string $type): AiPrompt
    {
        return AiPrompt::getByType($type);
    }

    /**
     * Apply prompt template with answers array
     *
     * @param AiPrompt $prompt The prompt configuration
     * @param array $answers Array of answers with format:
     *                      [
     *                          [
     *                              'id' => int,        // assessment_id
     *                              'answer' => string, // student's answer
     *                              'context' => string, // assessment context (optional)
     *                              'question' => string // assessment question (optional)
     *                          ]
     *                      ]
     * @return string The final prompt ready for AI processing
     * @throws InvalidArgumentException if answers format is invalid
     */
    public function applyPrompt(AiPrompt $prompt, array $answers): string
    {
        $this->validateAnswersFormat($answers);

        $formattedAnswers = [];

        foreach ($answers as $answer) {
            // Use default_context if context is empty/null
            $context = (!empty($answer['context']))
                ? $answer['context']
                : $prompt->default_context;

            // Use question if provided, otherwise empty string
            $question = $answer['question'] ?? '';

            // Apply item_replace_pattern to format the answer
            $formattedAnswer = str_replace(
                ['{id}', '{answer}', '{context}', '{question}'],
                [$answer['id'], $answer['answer'], $context, $question],
                $prompt->item_replace_pattern
            );

            $formattedAnswers[] = $formattedAnswer;
        }

        // Replace {array_of_sentence} in the main prompt with the formatted answer strings
        $finalPrompt = str_replace(
            '{array_of_sentence}',
            implode("\n", $formattedAnswers),
            $prompt->prompt
        );

        return $finalPrompt;
    }

    /**
     * Validate the answers array format
     *
     * @param array $answers
     * @throws InvalidArgumentException if format is invalid
     */
    private function validateAnswersFormat(array $answers): void
    {
        if (empty($answers)) {
            throw new InvalidArgumentException('Answers array cannot be empty');
        }

        foreach ($answers as $index => $answer) {
            if (!is_array($answer)) {
                throw new InvalidArgumentException("Answer at index {$index} must be an array");
            }

            if (!isset($answer['id']) || !is_int($answer['id'])) {
                throw new InvalidArgumentException("Answer at index {$index} must have a valid 'id' integer field");
            }

            if (!isset($answer['answer']) || !is_string($answer['answer'])) {
                throw new InvalidArgumentException("Answer at index {$index} must have a valid 'answer' string field");
            }

            // context is optional, but if provided, must be string
            if (isset($answer['context']) && !is_string($answer['context'])) {
                throw new InvalidArgumentException("Answer at index {$index} 'context' field must be a string if provided");
            }

            // question is optional, but if provided, must be string
            if (isset($answer['question']) && !is_string($answer['question'])) {
                throw new InvalidArgumentException("Answer at index {$index} 'question' field must be a string if provided");
            }
        }
    }

    /**
     * Update or create a prompt by type
     *
     * @param string $type
     * @param array $data
     * @return AiPrompt
     */
    public function updateOrCreatePrompt(string $type, array $data): AiPrompt
    {
        return AiPrompt::updateOrCreateByType($type, $data);
    }

    public function getResponseSchema()
    {
        return new Schema(
            type: DataType::OBJECT,
            properties: [
                'results' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(
                        type: DataType::OBJECT,
                        properties: [
                            'id' => new Schema(type: DataType::INTEGER),
                            'point' => new Schema(type: DataType::STRING),
                            'comment' => new Schema(type: DataType::STRING)
                        ],
                        required: ['id', 'point', 'comment']
                    )
                )
            ],
            required: ['results']
        );
    }

    public function callAiService(string $prompt, $aiPrompt = null): ?array
    {
        // Define the expected JSON schema for AI response
        $schema = $this->getResponseSchema();

        // Use database values if aiPrompt is provided, otherwise use defaults
        $temperature = $aiPrompt ? $aiPrompt->temperature : 0.3;
        $maxTokens = $aiPrompt ? $aiPrompt->max_tokens : 2000;

        $options = [
            'temperature' => $temperature,
            'max_tokens' => $maxTokens,
        ];

        // Try calling the AI service
        $geminiService = new GeminiService();
        $response = $geminiService->generateStructuredContentWithMetadata($prompt, $schema, $options);

        if ($response === null) {
            Log::warning('AI service returned null response, retrying once');

            // Retry once with different parameters (slightly higher temperature, lower tokens)
            $retryOptions = [
                'temperature' => $temperature + 0.2,
                'max_tokens' => max(1000, $maxTokens - 500),
            ];

            $response = $geminiService->generateStructuredContentWithMetadata($prompt, $schema, $retryOptions);
        }

        return $response;
    }

        /**
     * Validate AI response format
     *
     * @param array $response
     * @param array $expectedIds
     * @return array
     * @throws InvalidArgumentException
     */
    public function validateAiResponse(array $response, array $expectedIds): array
    {
        // Convert response to array recursively to handle stdClass objects
        $response = json_decode(json_encode($response), true);

        // Handle different response formats - be very flexible
        $results = null;

        // Check if it's wrapped in 'results' key
        if (isset($response['results']) && is_array($response['results'])) {
            $results = $response['results'];
        }
        // Check if it's a direct array of results
        elseif (is_array($response) && !empty($response)) {
            $results = $response;
        }

        if ($results === null || empty($results)) {
            throw new InvalidArgumentException('AI response must be an array of results or contain a results array. Received: ' . json_encode($response));
        }

        // Validate each result - be very flexible with data types
        $normalizedResults = [];
        foreach ($results as $index => $result) {
            // Convert to array if it's an object
            if (is_object($result)) {
                $result = (array) $result;
            }

            if (!is_array($result)) {
                throw new InvalidArgumentException("Result at index {$index} must be an array or object, got: " . gettype($result) . " - " . json_encode($result));
            }

            // Check required fields with flexible key matching
            $id = null;
            $point = null;
            $comment = null;

            // Try to find ID field (flexible matching)
            foreach (['id', 'ID', 'assessment_id', 'assessmentId'] as $idKey) {
                if (isset($result[$idKey])) {
                    $id = $result[$idKey];
                    break;
                }
            }

            // Try to find point field
            foreach (['point', 'points', 'score', 'rating'] as $pointKey) {
                if (isset($result[$pointKey])) {
                    $point = $result[$pointKey];
                    break;
                }
            }

            // Try to find comment field
            foreach (['comment', 'comments', 'feedback', 'explanation'] as $commentKey) {
                if (isset($result[$commentKey])) {
                    $comment = $result[$commentKey];
                    break;
                }
            }

            // Validate we found all required fields
            if ($id === null) {
                throw new InvalidArgumentException("Result at index {$index} is missing ID field. Available fields: " . implode(', ', array_keys($result)));
            }

            if ($point === null) {
                throw new InvalidArgumentException("Result at index {$index} is missing point/score field. Available fields: " . implode(', ', array_keys($result)));
            }

            if ($comment === null) {
                throw new InvalidArgumentException("Result at index {$index} is missing comment/feedback field. Available fields: " . implode(', ', array_keys($result)));
            }

            // Convert and validate types
            $resultId = (int) $id;
            $resultPoint = (string) $point;
            $resultComment = (string) $comment;

            // Validate that the ID is in our expected IDs
            if (!in_array($resultId, $expectedIds)) {
                throw new InvalidArgumentException("Result at index {$index} contains unexpected assessment ID: {$resultId}. Expected one of: " . implode(', ', $expectedIds));
            }

            $normalizedResults[] = [
                'id' => $resultId,
                'point' => $resultPoint,
                'comment' => $resultComment
            ];
        }

        // Check that we have results for all expected IDs
        $responseIds = array_column($normalizedResults, 'id');
        $missingIds = array_diff($expectedIds, $responseIds);

        if (!empty($missingIds)) {
            throw new InvalidArgumentException('AI response is missing results for assessment IDs: ' . implode(', ', $missingIds));
        }

        return $normalizedResults;
    }

    public function getResult(int $unitId, array $answers): array
    {
        $unit = Unit::findOrFail($unitId);
        $unitType = $unit->unit_type->value;
        $assessmentIds = collect($answers)->pluck('assessment_id')->toArray();
        $assessments = Assessment::whereIn('id', $assessmentIds)
            ->with('itemable')
            ->get()
            ->keyBy('id');

        $answersForAi = [];
        foreach ($answers as $answer) {
            $assessmentId = $answer['assessment_id'];
            
            if (!isset($assessments[$assessmentId])) {
                throw new \InvalidArgumentException("Assessment with ID {$assessmentId} not found for this unit");
            }
            
            $assessment = $assessments[$assessmentId];
            $answersForAi[] = [
                'id' => $assessmentId,
                'answer' => $answer['answer'],
                'context' => $assessment->getContext(),
                'question' => $assessment->getQuestion()
            ];
        }

        $aiPrompt = $this->getPromptByType($unitType);
        $finalPrompt = $this->applyPrompt(
            $aiPrompt,
            $answersForAi
        );

        $aiResponseWithMetadata = $this->callAiService($finalPrompt, $aiPrompt);
        $validatedResponse = $this->validateAiResponse($aiResponseWithMetadata['response'], $assessmentIds);

        return [
            'final_prompt' => $finalPrompt,
            'model_info' => $aiResponseWithMetadata['metadata'],
            'ai_output' => $validatedResponse
        ];
    }
}
