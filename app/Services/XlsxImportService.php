<?php

namespace App\Services;

use App\Models\Unit;
use App\Services\UnitService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as XlsxReader;

abstract class XlsxImportService
{
    /**
     * Default batch size for processing rows
     */
    protected const DEFAULT_BATCH_SIZE = 50;

    /**
     * Maximum memory usage threshold (in MB)
     */
    protected const MEMORY_THRESHOLD_MB = 128;

    public function __construct(
        protected UnitService $unitService
    ) {}

    /**
     * Get required headers for this assessment type
     */
    abstract protected function getRequiredHeaders(): array;

    /**
     * Validate individual assessment row data
     */
    abstract protected function validateAssessmentRow(array $row, int $rowNumber): void;

    /**
     * Create assessment for specific unit from row data
     */
    abstract protected function createAssessmentForUnit(array $row, Unit $unit): Model;

    /**
     * Generate XLSX template for download
     */
    abstract public function generateTemplateData(): array;

    /**
     * Get the assessment service instance
     */
    abstract protected function getAssessmentService(): \App\Services\AssessmentService;

    /**
     * Import assessments for a specific unit from XLSX file
     */
    public function importAssessmentsForUnit(UploadedFile $file, Unit $unit): array
    {
        $startTime = microtime(true);
        $initialMemory = memory_get_usage(true);

        // Validate file
        $this->validateFile($file);

        Log::info('Starting XLSX import', [
            'file_size' => $this->formatBytes($file->getSize()),
            'initial_memory' => $this->formatBytes($initialMemory),
            'unit_id' => $unit->id
        ]);

        try {
            // Use streaming parser for better memory efficiency
            $result = $this->processImportStreaming($file, $unit);

            $endTime = microtime(true);
            $finalMemory = memory_get_usage(true);
            $processingTime = round($endTime - $startTime, 2);

            Log::info('XLSX import completed', [
                'processing_time' => $processingTime . 's',
                'memory_used' => $this->formatBytes($finalMemory - $initialMemory),
                'peak_memory' => $this->formatBytes(memory_get_peak_usage(true)),
                'assessments_created' => $result['assessments_created']
            ]);

            $result['processing_time'] = $processingTime;
            return $result;

        } catch (\Exception $e) {
            Log::error('XLSX import failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'unit_id' => $unit->id
            ]);
            throw $e;
        }
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        $validator = Validator::make(
            ['file' => $file],
            [
                'file' => 'required|file|mimes:xlsx|max:10240', // 10MB max
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Process import using streaming for memory efficiency
     */
    private function processImportStreaming(UploadedFile $file, Unit $unit): array
    {
        $path = $file->store('temp', 'local');
        $fullPath = Storage::disk('local')->path($path);

        try {
            // Create reader with read filter for memory optimization
            $reader = new XlsxReader();
            $reader->setReadDataOnly(true);
            $reader->setReadEmptyCells(false);

            // Load only the worksheet structure first
            $spreadsheet = $reader->load($fullPath);
            $worksheet = $spreadsheet->getActiveSheet();

            // Get basic info
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();

            Log::info('XLSX file structure', [
                'rows' => $highestRow,
                'columns' => $highestColumn,
                'file' => $file->getClientOriginalName()
            ]);

            // Read and validate headers
            $headers = $this->readAndValidateHeaders($worksheet, $highestColumn);

            // Process data in batches
            return $this->processBatchedImport($worksheet, $headers, $highestRow, $unit);

        } finally {
            // Clean up temp file
            Storage::disk('local')->delete($path);
        }
    }

    /**
     * Read and validate headers from worksheet
     */
    private function readAndValidateHeaders($worksheet, string $highestColumn): array
    {
        $headerRow = $worksheet->rangeToArray('A1:' . $highestColumn . '1', null, true, false, false)[0];
        $headers = array_map('trim', array_filter($headerRow, function($value) {
            return !is_null($value) && $value !== '';
        }));

        $this->validateAssessmentHeaders($headers);
        return $headers;
    }

    /**
     * Process import in batches for memory efficiency
     */
    private function processBatchedImport($worksheet, array $headers, int $totalRows, Unit $unit): array
    {
        $batchSize = min(self::DEFAULT_BATCH_SIZE, 100); // Max 100 rows per batch
        $totalProcessed = 0;
        $createdAssessments = [];
        $errors = [];

        // Process in batches
        for ($startRow = 2; $startRow <= $totalRows; $startRow += $batchSize) {
            $endRow = min($startRow + $batchSize - 1, $totalRows);

            try {
                $batchResult = $this->processBatch($worksheet, $headers, $startRow, $endRow, $unit);
                $createdAssessments = array_merge($createdAssessments, $batchResult['assessments']);
                $totalProcessed += $batchResult['processed'];

                // Check memory usage
                $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
                if ($memoryUsage > self::MEMORY_THRESHOLD_MB) {
                    Log::warning('High memory usage during import', [
                        'memory_mb' => round($memoryUsage, 2),
                        'processed_rows' => $totalProcessed,
                        'current_batch' => "$startRow-$endRow"
                    ]);

                    // Force garbage collection
                    gc_collect_cycles();
                }

            } catch (\Exception $e) {
                $errors[] = [
                    'batch' => "$startRow-$endRow",
                    'error' => $e->getMessage()
                ];

                Log::error('Batch processing failed', [
                    'batch' => "$startRow-$endRow",
                    'error' => $e->getMessage()
                ]);

                // Continue with next batch or fail completely?
                // For now, we'll fail completely to maintain data integrity
                throw new \Exception(
                    "Import failed at rows $startRow-$endRow: " . $e->getMessage()
                );
            }
        }

        return [
            'assessments_created' => count($createdAssessments),
            'assessments' => $createdAssessments,
            'total_processed' => $totalProcessed,
            'errors' => $errors
        ];
    }

    /**
     * Process a single batch of rows
     */
    private function processBatch($worksheet, array $headers, int $startRow, int $endRow, Unit $unit): array
    {
        return DB::transaction(function () use ($worksheet, $headers, $startRow, $endRow, $unit) {
            $batchData = [];
            $processed = 0;

            // Read batch data
            for ($row = $startRow; $row <= $endRow; $row++) {
                $rowData = $this->readRowData($worksheet, $headers, $row);

                if (empty($rowData)) {
                    continue; // Skip empty rows
                }

                // Validate row
                $this->validateAssessmentRow($rowData, $row);
                $batchData[] = $rowData;
                $processed++;
            }

            // Create assessments for this batch
            $createdAssessments = [];
            foreach ($batchData as $rowData) {
                $assessment = $this->createAssessmentForUnit($rowData, $unit);
                $createdAssessments[] = $assessment;
            }

            return [
                'assessments' => $createdAssessments,
                'processed' => $processed
            ];
        });
    }

    /**
     * Read row data efficiently
     */
    private function readRowData($worksheet, array $headers, int $row): array
    {
        $highestColumn = chr(64 + count($headers)); // Convert to column letter
        $rowData = $worksheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, null, true, false, false)[0];

        // Skip empty rows
        if (count(array_filter($rowData, function($value) {
            return !is_null($value) && trim($value) !== '';
        })) === 0) {
            return [];
        }

        // Combine headers with row data
        $rowData = array_slice($rowData, 0, count($headers));
        $rowData = array_pad($rowData, count($headers), '');

        return array_combine($headers, array_map(function($value) {
            return is_null($value) ? '' : trim((string)$value);
        }, $rowData));
    }

    /**
     * Validate assessment XLSX headers
     */
    protected function validateAssessmentHeaders(array $headers): void
    {
        $requiredHeaders = $this->getRequiredHeaders();

        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            throw new \InvalidArgumentException(
                'Missing required headers: ' . implode(', ', $missingHeaders)
            );
        }
    }

    /**
     * Validate assessment XLSX data content
     */
    private function validateAssessmentXlsxData(array $data): void
    {
        foreach ($data as $index => $row) {
            $rowNumber = $index + 2; // +2 because of 0-based index and header row

            // Validate required fields
            $this->validateAssessmentRow($row, $rowNumber);
        }
    }



    /**
     * Format bytes for human readable display
     */
    protected function formatBytes(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Legacy method for backward compatibility - now uses streaming
     */
    protected function processAssessmentImport(array $data, Unit $unit): array
    {
        Log::warning('Using legacy processAssessmentImport method - consider using streaming import');

        $createdAssessments = [];

        foreach ($data as $row) {
            // Create assessment for the unit
            $assessment = $this->createAssessmentForUnit($row, $unit);
            $createdAssessments[] = $assessment;
        }

        return [
            'assessments_created' => count($createdAssessments),
            'assessments' => $createdAssessments
        ];
    }

}
