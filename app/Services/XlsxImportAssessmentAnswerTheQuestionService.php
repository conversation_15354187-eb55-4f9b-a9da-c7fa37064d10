<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\AssessmentAnswerTheQuestion;
use App\Services\UnitService;
use App\Services\AssessmentAnswerTheQuestionService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;

class XlsxImportAssessmentAnswerTheQuestionService extends XlsxImportService
{
    public function __construct(
        UnitService $unitService,
        private AssessmentAnswerTheQuestionService $assessmentService
    ) {
        parent::__construct($unitService);
    }

    /**
     * Generate template data for XLSX import template
     */
    public function generateTemplateData(): array
    {
        $headers = [
            'question',
            'correct_answer_1',
            'correct_answer_2',
            'correct_answer_3',
            'explanation'
        ];

        $sampleData = [
            [
                'What is the capital of France?',
                'Paris',
                'paris',
                '',
                'Paris is the capital and largest city of France.'
            ],
            [
                'What color do you get when you mix red and blue?',
                'Purple',
                'purple',
                'violet',
                'Mixing red and blue creates purple (or violet).'
            ],
            [
                'What is 2 + 2?',
                '4',
                'four',
                'Four',
                'Two plus two equals four.'
            ]
        ];

        return [
            'headers' => $headers,
            'sample_data' => $sampleData,
            'filename' => 'answer_the_question_import_template.xlsx'
        ];
    }

    /**
     * Get required headers for answer the question assessments
     */
    protected function getRequiredHeaders(): array
    {
        return [
            'question',
            'correct_answer_1'
        ];
    }

    /**
     * Get the assessment service instance
     */
    protected function getAssessmentService(): \App\Services\AssessmentService
    {
        return $this->assessmentService;
    }

    /**
     * Validate individual assessment row data for answer the question
     */
    protected function validateAssessmentRow(array $row, int $rowNumber): void
    {
        // Build dynamic rules for all correct_answer fields
        $rules = [
            'question' => 'required|string|max:1000',
            'explanation' => 'nullable|string|max:2000'
        ];

        // Add validation rules for all correct_answer fields dynamically
        $answerCount = 0;
        foreach ($row as $key => $value) {
            if (preg_match('/^correct_answer_(\d+)$/', $key, $matches)) {
                $answerNumber = (int) $matches[1];
                $answerCount = max($answerCount, $answerNumber);

                // First answer is required, rest are optional
                if ($answerNumber === 1) {
                    $rules[$key] = 'required|string|max:500';
                } else {
                    $rules[$key] = 'nullable|string|max:500';
                }
            }
        }

        // Ensure we have at least 1 answer
        if ($answerCount < 1) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: At least 1 answer (correct_answer_1) is required"
            );
        }

        $validator = Validator::make($row, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: " . implode(', ', $errors)
            );
        }

        // Count non-empty answers
        $nonEmptyAnswers = 0;
        for ($i = 1; $i <= $answerCount; $i++) {
            $answerKey = "correct_answer_{$i}";
            if (isset($row[$answerKey]) && !empty(trim($row[$answerKey]))) {
                $nonEmptyAnswers++;
            }
        }

        // Ensure at least one answer is provided
        if ($nonEmptyAnswers === 0) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: At least one correct answer must be provided"
            );
        }
    }

    /**
     * Create assessment for specific unit from row data
     */
    protected function createAssessmentForUnit(array $row, Unit $unit): Model
    {
        // Dynamically build correct answers array from all correct_answer fields
        $correctAnswers = [];

        // Find all correct_answer fields and sort them numerically
        $answerFields = [];
        foreach ($row as $key => $value) {
            if (preg_match('/^correct_answer_(\d+)$/', $key, $matches)) {
                $answerFields[(int) $matches[1]] = $value;
            }
        }
        ksort($answerFields);

        // Build correct answers array (filter out empty answers)
        foreach ($answerFields as $answer) {
            if (!empty(trim($answer))) {
                $correctAnswers[] = trim($answer);
            }
        }

        $assessmentData = [
            'question' => $row['question'],
            'explanation' => isset($row['explanation']) && $row['explanation'] ? $row['explanation'] : null,
            'correct_answers' => $correctAnswers
        ];

        // Create assessment
        $assessment = $this->assessmentService->create($assessmentData);

        // Attach to unit (get next assessment order)
        $nextOrder = $unit->assessments()->max('assessment_order') + 1;
        $this->assessmentService->attachToUnits($assessment, [
            ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
        ]);

        return $assessment;
    }

    /**
     * Override header validation to include answer count check
     */
    protected function validateAssessmentHeaders(array $headers): void
    {
        parent::validateAssessmentHeaders($headers);

        // Check for at least one correct_answer header
        $answerHeaders = array_filter($headers, function($header) {
            return preg_match('/^correct_answer_\d+$/', $header);
        });

        if (count($answerHeaders) < 1) {
            throw new \InvalidArgumentException(
                'At least 1 answer header (correct_answer_1) is required'
            );
        }
    }
}
