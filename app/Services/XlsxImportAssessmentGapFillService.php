<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\AssessmentGapFill;
use App\Services\UnitService;
use App\Services\AssessmentGapFillService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;

class XlsxImportAssessmentGapFillService extends XlsxImportService
{
    public function __construct(
        UnitService $unitService,
        private AssessmentGapFillService $assessmentService
    ) {
        parent::__construct($unitService);
    }

    /**
     * Generate template data for XLSX import template
     */
    public function generateTemplateData(): array
    {
        $headers = [
            'question',
            'correct_answer_1',
            'correct_answer_2',
            'correct_answer_3',
            'explanation'
        ];

        $sampleData = [
            [
                'I ___ to the store yesterday to buy ____ for dinner.',
                'went',
                'food',
                '',
                'Simple past tense with "went" and "food" as the missing words.'
            ],
            [
                'The cat ___ on the mat.',
                'sat',
                '',
                '',
                'Simple past tense describing the cat\'s position.'
            ],
            [
                'She _______ her homework before _____ dinner with her _______ family.',
                'finished',
                'eating',
                'loving',
                'Past tense showing sequence of actions with descriptive adjective.'
            ]
        ];

        return [
            'headers' => $headers,
            'sample_data' => $sampleData,
            'filename' => 'gap_fill_import_template.xlsx'
        ];
    }

    /**
     * Get required headers for gap fill assessments
     */
    protected function getRequiredHeaders(): array
    {
        return [
            'question',
            'correct_answer_1',
            'explanation'
        ];
    }

    /**
     * Get the assessment service instance
     */
    protected function getAssessmentService(): \App\Services\AssessmentService
    {
        return $this->assessmentService;
    }

    /**
     * Validate individual assessment row data for gap fill
     */
    protected function validateAssessmentRow(array $row, int $rowNumber): void
    {
        // Build dynamic rules for all correct_answer fields
        $rules = [
            'question' => 'required|string|max:1000',
            'explanation' => 'nullable|string|max:2000'
        ];

        // Add validation rules for all correct_answer fields dynamically
        $answerCount = 0;
        foreach ($row as $key => $value) {
            if (preg_match('/^correct_answer_(\d+)$/', $key, $matches)) {
                $answerNumber = (int) $matches[1];
                $answerCount = max($answerCount, $answerNumber);

                // First answer is required, rest are optional
                if ($answerNumber === 1) {
                    $rules[$key] = 'required|string|max:500';
                } else {
                    $rules[$key] = 'nullable|string|max:500';
                }
            }
        }

        // Ensure we have at least 1 answer
        if ($answerCount < 1) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: At least 1 answer (correct_answer_1) is required"
            );
        }

        $validator = Validator::make($row, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: " . implode(', ', $errors)
            );
        }

        // Validate that question contains placeholders (underscores)
        $gapCount = $this->countGapPlaceholders($row['question']);
        if ($gapCount === 0) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: Question must contain gap placeholders (use at least 3 underscores ___ for each gap)"
            );
        }

        // Count non-empty answers
        $nonEmptyAnswers = 0;
        for ($i = 1; $i <= $answerCount; $i++) {
            $answerKey = "correct_answer_{$i}";
            if (isset($row[$answerKey]) && !empty(trim($row[$answerKey]))) {
                $nonEmptyAnswers++;
            }
        }

        // Validate that the number of gaps matches the number of answers
        if ($gapCount !== $nonEmptyAnswers) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: Number of gaps in question ({$gapCount}) must match number of provided answers ({$nonEmptyAnswers})"
            );
        }
    }

    /**
     * Create assessment for specific unit from row data
     */
    protected function createAssessmentForUnit(array $row, Unit $unit): Model
    {
        // Dynamically build correct answers array from all correct_answer fields
        $correctAnswers = [];

        // Find all correct_answer fields and sort them numerically
        $answerFields = [];
        foreach ($row as $key => $value) {
            if (preg_match('/^correct_answer_(\d+)$/', $key, $matches)) {
                $answerFields[(int) $matches[1]] = $value;
            }
        }
        ksort($answerFields);

        // Build correct answers array (filter out empty answers)
        foreach ($answerFields as $answer) {
            if (!empty(trim($answer))) {
                $correctAnswers[] = trim($answer);
            }
        }

        $assessmentData = [
            'question' => $row['question'],
            'explanation' => isset($row['explanation']) && $row['explanation'] ? $row['explanation'] : null,
            'correct_answers' => $correctAnswers
        ];

        // Create assessment
        $assessment = $this->assessmentService->create($assessmentData);

        // Attach to unit (get next assessment order)
        $nextOrder = $unit->assessments()->max('assessment_order') + 1;
        $this->assessmentService->attachToUnits($assessment, [
            ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
        ]);

        return $assessment;
    }

    /**
     * Override header validation to include answer count check
     */
    protected function validateAssessmentHeaders(array $headers): void
    {
        parent::validateAssessmentHeaders($headers);

        // Check for at least one correct_answer header
        $answerHeaders = array_filter($headers, function($header) {
            return preg_match('/^correct_answer_\d+$/', $header);
        });

        if (count($answerHeaders) < 1) {
            throw new \InvalidArgumentException(
                'At least 1 answer header (correct_answer_1) is required'
            );
        }
    }

    /**
     * Count gap placeholders in a question string
     * Supports underscore groups of any length (minimum 3 consecutive underscores)
     * 
     * @param string $question
     * @return int Number of gap placeholders found
     */
    private function countGapPlaceholders(string $question): int
    {
        // Use regex to find all groups of 3 or more consecutive underscores
        preg_match_all('/_{3,}/', $question, $matches);
        
        return count($matches[0]);
    }
}
