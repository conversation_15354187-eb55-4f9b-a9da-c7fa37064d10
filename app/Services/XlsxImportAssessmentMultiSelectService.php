<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\AssessmentMultipleSelect;
use App\Services\UnitService;
use App\Services\AssessmentMultipleSelectService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;

class XlsxImportAssessmentMultiSelectService extends XlsxImportService
{
    public function __construct(
        UnitService $unitService,
        private AssessmentMultipleSelectService $assessmentService
    ) {
        parent::__construct($unitService);
    }

    /**
     * Generate template data for XLSX import template
     */
    public function generateTemplateData(): array
    {
        $headers = [
            'question',
            'option_1',
            'option_2',
            'option_3',
            'option_4',
            'option_5',
            'option_6',
            'correct_answer',
            'explanation'
        ];

        $sampleData = [
            [
                'What is the English word for "hello"?',
                'Hello',
                'Goodbye',
                'Please',
                'Thank you',
                'Sorry',
                'Welcome',
                'Hello',
                'Hello is a common greeting used when meeting someone'
            ],
            [
                'How do you say "goodbye" in English?',
                'Hello',
                'Goodbye',
                'Please',
                'Sorry',
                '',
                '',
                'Goodbye',
                'Goodbye is used when leaving or parting from someone'
            ],
            [
                'Which word means "please"?',
                'Hello',
                'Goodbye',
                'Please',
                '',
                '',
                '',
                'Please',
                'Please is used to make polite requests'
            ]
        ];

        return [
            'headers' => $headers,
            'sample_data' => $sampleData,
            'filename' => 'multiple_select_import_template.xlsx'
        ];
    }

    /**
     * Get required headers for multiple select assessments
     */
    protected function getRequiredHeaders(): array
    {
        return [
            'question',
            'option_1',
            'option_2',
            'correct_answer'
        ];
    }

    /**
     * Get the assessment service instance
     */
    protected function getAssessmentService(): \App\Services\AssessmentService
    {
        return $this->assessmentService;
    }

    /**
     * Validate individual assessment row data for multiple select
     */
    protected function validateAssessmentRow(array $row, int $rowNumber): void
    {
        $errors = [];
        $warnings = [];

        // Build dynamic rules for all option fields
        $rules = [
            'question' => 'required|string|max:1000',
            'correct_answer' => 'required|string',
            'explanation' => 'nullable|string|max:1000'
        ];

        // Add validation rules for all option fields dynamically
        $optionCount = 0;
        foreach ($row as $key => $value) {
            if (preg_match('/^option_(\d+)$/', $key, $matches)) {
                $optionNumber = (int) $matches[1];
                $optionCount = max($optionCount, $optionNumber);

                // First two options are required, rest are optional
                if ($optionNumber <= 2) {
                    $rules[$key] = 'required|string|max:500';
                } else {
                    $rules[$key] = 'nullable|string|max:500';
                }
            }
        }

        // Ensure we have at least 2 options
        if ($optionCount < 2) {
            $errors[] = "At least 2 options (option_1, option_2) are required";
        }

        $validator = Validator::make($row, $rules);

        if ($validator->fails()) {
            $errors = array_merge($errors, $validator->errors()->all());
        }

        // Collect all option values (filter out empty ones)
        $options = [];
        for ($i = 1; $i <= $optionCount; $i++) {
            $optionKey = "option_{$i}";
            if (isset($row[$optionKey]) && !empty(trim($row[$optionKey]))) {
                $options[] = trim($row[$optionKey]);
            }
        }

        // Validate correct_answer is one of the options
        if (!empty($options) && !in_array(trim($row['correct_answer']), $options)) {
            $errors[] = "correct_answer must match one of the provided options";
        }

        // Check for duplicate options
        if (count($options) !== count(array_unique($options))) {
            $warnings[] = "Duplicate options detected - this may confuse students";
        }

        // Check question length
        if (isset($row['question']) && strlen($row['question']) < 10) {
            $warnings[] = "Question is very short - consider adding more context";
        }

        // Log warnings if any
        if (!empty($warnings)) {
            \Log::info("Row {$rowNumber} validation warnings", $warnings);
        }

        // Throw error if validation failed
        if (!empty($errors)) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: " . implode(', ', $errors)
            );
        }
    }

    /**
     * Create assessment for specific unit from row data
     */
    protected function createAssessmentForUnit(array $row, Unit $unit): Model
    {
        try {
            // Dynamically build answer list from all option fields
            $answerList = [];

            // Find all option fields and sort them numerically
            $optionFields = [];
            foreach ($row as $key => $value) {
                if (preg_match('/^option_(\d+)$/', $key, $matches)) {
                    $optionFields[(int) $matches[1]] = $value;
                }
            }
            ksort($optionFields);

            // Build answer list (filter out empty options)
            foreach ($optionFields as $option) {
                if (!empty(trim($option))) {
                    $answerList[] = trim($option);
                }
            }

            // Find correct answer index
            $correctAnswerIndex = array_search(trim($row['correct_answer']), $answerList);

            if ($correctAnswerIndex === false) {
                throw new \InvalidArgumentException('Correct answer not found in options list');
            }

            $assessmentData = [
                'question' => trim($row['question']),
                'answer_list' => $answerList,
                'correct_answer_indexes' => [$correctAnswerIndex],
                'explanations' => isset($row['explanation']) && !empty(trim($row['explanation']))
                    ? [trim($row['explanation'])]
                    : []
            ];

            // Create assessment with retry logic
            $assessment = $this->createAssessmentWithRetry($assessmentData);

            // Attach to unit (get next assessment order with proper ordering)
            $nextOrder = $this->getNextAssessmentOrder($unit);
            $this->assessmentService->attachToUnits($assessment, [
                ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
            ]);

            return $assessment;

        } catch (\Exception $e) {
            \Log::error('Failed to create assessment from row data', [
                'row_data' => $row,
                'unit_id' => $unit->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Create assessment with retry logic for transient failures
     */
    private function createAssessmentWithRetry(array $assessmentData, int $maxRetries = 3): Model
    {
        $lastException = null;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                return $this->assessmentService->create($assessmentData);
            } catch (\Exception $e) {
                $lastException = $e;

                if ($attempt < $maxRetries) {
                    \Log::warning("Assessment creation attempt {$attempt} failed, retrying", [
                        'error' => $e->getMessage(),
                        'attempt' => $attempt
                    ]);

                    // Brief delay before retry
                    usleep(100000); // 100ms
                } else {
                    \Log::error("Assessment creation failed after {$maxRetries} attempts", [
                        'error' => $e->getMessage(),
                        'assessment_data' => $assessmentData
                    ]);
                }
            }
        }

        throw $lastException;
    }

    /**
     * Get next assessment order with proper locking
     */
    private function getNextAssessmentOrder(Unit $unit): int
    {
        // Use a simple approach - get max order and increment
        // In high concurrency scenarios, this could be enhanced with database locking
        $maxOrder = $unit->assessments()->max('assessment_order') ?? 0;
        return $maxOrder + 1;
    }

    /**
     * Override header validation to include option count check
     */
    protected function validateAssessmentHeaders(array $headers): void
    {
        parent::validateAssessmentHeaders($headers);

        // Count option headers to ensure we have at least 2
        $optionHeaders = array_filter($headers, function($header) {
            return preg_match('/^option_\d+$/', $header);
        });

        if (count($optionHeaders) < 2) {
            throw new \InvalidArgumentException(
                'At least 2 option headers (option_1, option_2) are required'
            );
        }
    }
}
