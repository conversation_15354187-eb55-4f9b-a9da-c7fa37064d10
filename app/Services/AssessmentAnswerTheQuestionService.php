<?php

namespace App\Services;

use App\Models\AssessmentAnswerTheQuestion;
use Illuminate\Database\Eloquent\Model;

class AssessmentAnswerTheQuestionService extends AssessmentService
{
    /**
     * Create a new AssessmentAnswerTheQuestion with its corresponding Assessment
     */
    public function create(array $data): AssessmentAnswerTheQuestion
    {
        // Create the AssessmentAnswerTheQuestion first
        $assessmentAnswerTheQuestion = AssessmentAnswerTheQuestion::create([
            'question' => $data['question'],
            'correct_answers' => $data['correct_answers'],
            'explanation' => $data['explanation'] ?? null,
        ]);

        // Create the polymorphic Assessment record and load relationship
        return $this->createAssessmentRecord($assessmentAnswerTheQuestion);
    }

    public function update(Model $assessment, array $data): Model
    {
        $assessment->update([
            'question' => $data['question'] ?? $assessment->question,
            'correct_answers' => $data['correct_answers'] ?? $assessment->correct_answers,
            'explanation' => $data['explanation'] ?? $assessment->explanation,
        ]);

        return $assessment->fresh();
    }

    public function score(Model $assessment, array $userAnswer): array
    {
        // Get correct answers and normalize them (trim whitespace and convert to lowercase)
        $correctAnswers = array_map('strtolower', array_map('trim', $assessment->correct_answers));

        // Get user answer and normalize it
        $userAnswerText = strtolower(trim($userAnswer[0] ?? ''));

        // Check if user answer matches any of the correct answers
        $isCorrect = in_array($userAnswerText, $correctAnswers);

        // Generate explanation
        $explanation = $assessment->explanation ?? 'Correct answer(s): ' . implode(', ', $assessment->correct_answers);

        return [
            'explain' => $explanation,
            'pass' => $isCorrect,
            'meta' => null
        ];
    }
}
