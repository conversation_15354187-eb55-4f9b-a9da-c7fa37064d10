<?php

namespace App\Services;

use App\Models\AssessmentMultipleSelect;
use Illuminate\Database\Eloquent\Model;

class AssessmentMultipleSelectService extends AssessmentService
{
    /**
     * Create a new AssessmentMultipleSelect with its corresponding Assessment
     */
    public function create(array $data): AssessmentMultipleSelect
    {
        // Create the AssessmentMultipleSelect first
        $assessmentMultipleSelect = AssessmentMultipleSelect::create([
            'question' => $data['question'],
            'answer_list' => $data['answer_list'],
            'correct_answer_indexes' => $data['correct_answer_indexes'],
            'explanations' => $data['explanations'] ?? [],
        ]);

        // Create the polymorphic Assessment record and load relationship
        return $this->createAssessmentRecord($assessmentMultipleSelect);
    }

    public function update(Model $assessment, array $data): Model
    {
        $assessment->update([
            'question' => $data['question'] ?? $assessment->question,
            'answer_list' => $data['answer_list'] ?? $assessment->answer_list,
            'correct_answer_indexes' => $data['correct_answer_indexes'] ?? $assessment->correct_answer_indexes,
            'explanations' => $data['explanations'] ?? $assessment->explanations,
        ]);

        return $assessment->fresh();
    }

    public function score(Model $assessment, array $userAnswer): array
    {
        $correctIndexes = $assessment->correct_answer_indexes;
        $explanations = $assessment->explanations ?? [];

        // Normalize user input - ensure array and convert to integers
        $userSelectedIndexes = array_map('intval', $userAnswer);
        sort($userSelectedIndexes);

        // Normalize correct indexes
        $correctIndexesSorted = array_map('intval', $correctIndexes);
        sort($correctIndexesSorted);

        // Check if user selection matches correct answers exactly
        $isCorrect = $userSelectedIndexes === $correctIndexesSorted;

        // Generate explanation
        $explanation = '';
        if (!empty($explanations) && count($explanations) > 0) {
            $explanation = $explanations[0]; // Use first explanation
        } else {
            // Generate default explanation
            $correctAnswers = [];
            foreach ($correctIndexes as $index) {
                if (isset($assessment->answer_list[$index])) {
                    $correctAnswers[] = $assessment->answer_list[$index];
                }
            }
            $explanation = 'The correct answer(s): ' . implode(', ', $correctAnswers);
        }

        return [
            'explain' => $explanation,
            'pass' => $isCorrect,
            'meta' => null
        ];
    }
}
