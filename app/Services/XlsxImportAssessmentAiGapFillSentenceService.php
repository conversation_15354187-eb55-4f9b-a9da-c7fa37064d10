<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\AssessmentAiGapFillSentence;
use App\Services\UnitService;
use App\Services\AssessmentAiGapFillSentenceService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;

class XlsxImportAssessmentAiGapFillSentenceService extends XlsxImportService
{
    public function __construct(
        UnitService $unitService,
        private AssessmentAiGapFillSentenceService $assessmentService
    ) {
        parent::__construct($unitService);
    }

    /**
     * Generate template data for XLSX import template
     */
    public function generateTemplateData(): array
    {
        $headers = [
            'question',
            'context'
        ];

        $sampleData = [
            [
                'I ____ to the store yesterday.',
                'A sentence about going to a store in the past tense.'
            ],
            [
                'The cat ____ on the mat and ____ peacefully.',
                'A sentence describing a cat\'s actions with two gaps to fill.'
            ],
            [
                'She _____ her homework before _____ dinner.',
                'A sentence about completing tasks in sequence.'
            ]
        ];

        return [
            'headers' => $headers,
            'sample_data' => $sampleData,
            'filename' => 'ai_gap_fill_sentence_import_template.xlsx'
        ];
    }

    /**
     * Get required headers for AI gap fill sentence assessments
     */
    protected function getRequiredHeaders(): array
    {
        return [
            'question',
            'context'
        ];
    }

    /**
     * Get the assessment service instance
     */
    protected function getAssessmentService(): \App\Services\AssessmentService
    {
        return $this->assessmentService;
    }

    /**
     * Validate individual assessment row data for AI gap fill sentence
     */
    protected function validateAssessmentRow(array $row, int $rowNumber): void
    {
        $rules = [
            'question' => 'required|string|max:1000',
            'context' => 'required|string|max:2000'
        ];

        $validator = Validator::make($row, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: " . implode(', ', $errors)
            );
        }

        // Validate that question contains underscores for gaps
        if (!preg_match('/_{1,}/', $row['question'])) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: question must contain underscores (____) to indicate gaps to fill"
            );
        }
    }

    /**
     * Create assessment for specific unit from row data
     */
    protected function createAssessmentForUnit(array $row, Unit $unit): Model
    {
        $assessmentData = [
            'question' => $row['question'],
            'context' => $row['context']
        ];

        // Create assessment
        $assessment = $this->assessmentService->create($assessmentData);

        // Attach to unit (get next assessment order)
        $nextOrder = $unit->assessments()->max('assessment_order') + 1;
        $this->assessmentService->attachToUnits($assessment, [
            ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
        ]);

        return $assessment;
    }
}
