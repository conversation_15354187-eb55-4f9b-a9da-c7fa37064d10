<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Modules\Storage\Traits\HasFiles;

class AssessmentGapFill extends Model
{
    use HasFactory, HasFiles;

    protected $fillable = [
        'question',
        'explanation',
        'correct_answers',
    ];

    protected $casts = [
        'correct_answers' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function assessment(): MorphOne
    {
        return $this->morphOne(Assessment::class, 'itemable');
    }
}
