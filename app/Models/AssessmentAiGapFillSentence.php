<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Modules\Storage\Traits\HasFiles;
use Modules\Storage\Models\File;
use Modules\Storage\Http\Resources\FileResource;

class AssessmentAiGapFillSentence extends Model
{
    use HasFactory, HasFiles;

    protected $fillable = [
        'question',
        'context',
    ];

    protected $casts = [
        'fill_position' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function assessment(): MorphOne
    {
        return $this->morphOne(Assessment::class, 'itemable');
    }

    public function files()
    {
        return $this->morphMany(File::class, 'fileable');
    }

    /**
     * Get formatted files with only id and public URL
     */
    public function getFormattedFilesAttribute()
    {
        return FileResource::collection($this->files)->resolve();
    }

    /**
     * Boot the model to auto-calculate fill_position from question
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->fill_position = $model->calculateFillPositions($model->question);
        });

        static::updating(function ($model) {
            if ($model->isDirty('question')) {
                $model->fill_position = $model->calculateFillPositions($model->question);
            }
        });
    }

    /**
     * Calculate fill positions from question string
     * Finds all occurrences of underscore sequences (____) and returns their start positions
     * Supports multiple gaps: "I ____ to the store and bought _____ apples" -> [2, 35]
     */
    public function calculateFillPositions(string $question): array
    {
        $positions = [];
        $pattern = '/_{1,}/'; // Match one or more underscores
        $offset = 0;

        while (preg_match($pattern, $question, $matches, PREG_OFFSET_CAPTURE, $offset)) {
            $positions[] = $matches[0][1]; // Store the start position (index)
            $offset = $matches[0][1] + strlen($matches[0][0]); // Move past this match
        }

        return $positions;
    }
}