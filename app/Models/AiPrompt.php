<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiPrompt extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'prompt',
        'item_replace_pattern',
        'default_context',
        'temperature',
        'max_tokens',
    ];

    protected $casts = [
        'type' => 'string',
        'prompt' => 'string',
        'item_replace_pattern' => 'string',
        'default_context' => 'string',
        'temperature' => 'decimal:2',
        'max_tokens' => 'integer',
    ];

    /**
     * Get AI prompt by type
     */
    public static function getByType(string $type): self
    {
        return self::where('type', $type)->firstOrFail();
    }

    /**
     * Update or create a prompt by type
     */
    public static function updateOrCreateByType(string $type, array $data): self
    {
        return self::updateOrCreate(['type' => $type], $data);
    }
}
