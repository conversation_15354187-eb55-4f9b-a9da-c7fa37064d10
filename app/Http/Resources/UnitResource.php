<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UnitResource extends JsonResource
{
    private bool $hideAnswers;

    public function __construct($resource, bool $hideAnswers = false)
    {
        parent::__construct($resource);
        $this->hideAnswers = $hideAnswers;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'course_id' => $this->course_id,
            'title' => $this->title,
            'description' => $this->description,
            'skill_type' => $this->skill_type,
            'difficulty' => $this->difficulty,
            'unit_type' => $this->unit_type,
            'unit_order' => $this->unit_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'course' => $this->whenLoaded('course'),
            'assessments' => $this->getDirectAssessments()->map(function ($assessment) {
                if ($assessment instanceof \App\Models\AssessmentMultipleSelect) {
                    return new AssessmentMultipleSelectResource($assessment, $this->hideAnswers);
                } elseif ($assessment instanceof \App\Models\AssessmentGapFill) {
                    return new AssessmentGapFillResource($assessment, $this->hideAnswers);
                } elseif ($assessment instanceof \App\Models\AssessmentAiGapFillSentence) {
                    return new AssessmentAiGapFillSentenceResource($assessment, $this->hideAnswers);
                }
                return $assessment;
            }),
        ];
    }
}
