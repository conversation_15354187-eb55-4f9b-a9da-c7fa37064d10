<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    private bool $hideAnswers;

    public function __construct($resource, bool $hideAnswers = false)
    {
        parent::__construct($resource);
        $this->hideAnswers = $hideAnswers;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'units' => $this->whenLoaded('units', function () {
                return $this->units->map(function ($unit) {
                    return new UnitResource($unit, $this->hideAnswers);
                });
            }),
        ];
    }
}
