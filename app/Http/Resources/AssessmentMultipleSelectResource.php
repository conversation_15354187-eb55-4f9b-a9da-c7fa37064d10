<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssessmentMultipleSelectResource extends JsonResource
{
    private bool $hideAnswers;

    public function __construct($resource, bool $hideAnswers = false)
    {
        parent::__construct($resource);
        $this->hideAnswers = $hideAnswers;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'question' => $this->question,
            'answer_list' => $this->answer_list,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'files' => $this->whenLoaded('files'),
            'assessment_order' => $this->assessment_order ?? null,
            'assessment_id' => $this->assessment_id ?? null,
        ];

        // Include answers only if not hidden
        if (!$this->hideAnswers) {
            $data['correct_answer_indexes'] = $this->correct_answer_indexes;
        }

        return $data;
    }
}
