<?php

namespace App\Http\Traits;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

trait HandlesApiResponses
{
    /**
     * Return a successful JSON response
     */
    protected function successResponse(string $message, $data = null, int $statusCode = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $statusCode);
    }

    /**
     * Return an error JSON response
     */
    protected function errorResponse(string $message, $error = null, int $statusCode = 500): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message
        ];

        if ($error !== null) {
            $response['error'] = $error;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Handle exceptions and return appropriate JSON responses
     */
    protected function handleException(\Throwable $exception, string $operation = 'operation', string $context = null): JsonResponse
    {
        // Log the exception with context
        $logContext = array_filter([
            'operation' => $operation,
            'context' => $context,
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
        ]);

        Log::error("API Exception in {$operation}", $logContext);

        // Handle specific exception types
        return match (true) {
            $exception instanceof ModelNotFoundException => $this->errorResponse(
                'Resource not found',
                'The requested resource does not exist',
                404
            ),
            $exception instanceof ValidationException => $this->errorResponse(
                'Validation failed',
                $exception->errors(),
                422
            ),
            $exception instanceof \InvalidArgumentException => $this->errorResponse(
                'Invalid request data',
                $exception->getMessage(),
                400
            ),
            default => $this->errorResponse(
                "Failed to {$operation}",
                config('app.debug') ? $exception->getMessage() : 'An unexpected error occurred',
                500
            )
        };
    }

    /**
     * Execute an operation and handle exceptions
     */
    protected function executeWithErrorHandling(callable $operation, string $operationName, string $context = null): JsonResponse
    {
        try {
            return $operation();
        } catch (\Throwable $exception) {
            return $this->handleException($exception, $operationName, $context);
        }
    }
}