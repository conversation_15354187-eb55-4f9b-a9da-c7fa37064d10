<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TestPromptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'unit_id' => 'required|integer|exists:units,id',
            'answers' => 'required|array|min:1',
            'answers.*.assessment_id' => 'required|integer|exists:assessments,id',
            'answers.*.answer' => 'required|string',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'unit_id.required' => 'Unit ID is required',
            'unit_id.exists' => 'The specified unit does not exist',
            'answers.required' => 'Answers array is required',
            'answers.min' => 'At least one answer is required',
            'answers.*.assessment_id.required' => 'Assessment ID is required for each answer',
            'answers.*.assessment_id.exists' => 'One or more assessment IDs do not exist',
            'answers.*.answer.required' => 'Answer text is required for each assessment',
        ];
    }
}
