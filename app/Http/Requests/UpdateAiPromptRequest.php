<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAiPromptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'prompt' => 'required|string|min:10',
            'item_replace_pattern' => 'required|string|min:5',
            'default_context' => 'nullable|string',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'prompt.required' => 'The AI prompt is required',
            'prompt.min' => 'The AI prompt must be at least 10 characters',
            'item_replace_pattern.required' => 'The item replace pattern is required',
            'item_replace_pattern.min' => 'The item replace pattern must be at least 5 characters',
        ];
    }
}
