<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImportXlsxRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => 'required|file|mimes:xlsx|max:25600', // 25MB max (increased for larger imports)
            'assessment_type' => 'nullable|string|in:multiple_select,ai_gap_fill_sentence,gap_fill',
            'batch_size' => 'nullable|integer|min:10|max:200', // Optional batch size override
            'validate_only' => 'nullable|boolean', // Option to validate without importing
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $file = $this->file('file');

            if ($file) {
                // Additional file validation
                $extension = strtolower($file->getClientOriginalExtension());

                if ($extension !== 'xlsx') {
                    $validator->errors()->add('file', 'File must be an Excel file with .xlsx extension.');
                }

                // Check if file is readable
                if (!$file->isValid()) {
                    $validator->errors()->add('file', 'The uploaded file is not valid or corrupted.');
                }

                // Check file size more precisely
                $maxSizeBytes = 25 * 1024 * 1024; // 25MB
                if ($file->getSize() > $maxSizeBytes) {
                    $validator->errors()->add('file', 
                        'File size (' . $this->formatBytes($file->getSize()) . ') exceeds maximum allowed size of 25MB.'
                    );
                }

                // Basic XLSX file structure validation
                if (!$this->isValidXlsxStructure($file)) {
                    $validator->errors()->add('file', 'The uploaded file does not appear to be a valid XLSX file.');
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'An XLSX file is required for import.',
            'file.file' => 'The uploaded item must be a file.',
            'file.mimes' => 'The file must be an Excel file (.xlsx extension).',
            'file.max' => 'The file size must not exceed 25MB.',
            'assessment_type.in' => 'Assessment type must be one of: multiple_select, ai_gap_fill_sentence, gap_fill.',
            'batch_size.integer' => 'Batch size must be a valid integer.',
            'batch_size.min' => 'Batch size must be at least 10.',
            'batch_size.max' => 'Batch size cannot exceed 200.',
            'validate_only.boolean' => 'Validate only must be true or false.',
        ];
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'XLSX file',
            'assessment_type' => 'assessment type',
            'batch_size' => 'batch size',
            'validate_only' => 'validation mode',
        ];
    }

    /**
     * Get the validated file
     */
    public function getFile(): \Illuminate\Http\UploadedFile
    {
        return $this->validated()['file'];
    }

    /**
     * Get batch size with default
     */
    public function getBatchSize(): int
    {
        return $this->validated()['batch_size'] ?? 50;
    }

    /**
     * Check if this is validation-only request
     */
    public function isValidationOnly(): bool
    {
        return $this->validated()['validate_only'] ?? false;
    }

    /**
     * Format bytes for human readable display
     */
    private function formatBytes(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Basic XLSX file structure validation
     */
    private function isValidXlsxStructure(\Illuminate\Http\UploadedFile $file): bool
    {
        try {
            // Check if file can be opened as ZIP (XLSX is a ZIP archive)
            $zip = new \ZipArchive();
            $result = $zip->open($file->getPathname());
            
            if ($result !== true) {
                return false;
            }
            
            // Check for required XLSX files
            $requiredFiles = [
                '[Content_Types].xml',
                '_rels/.rels',
                'xl/workbook.xml'
            ];
            
            foreach ($requiredFiles as $requiredFile) {
                if ($zip->locateName($requiredFile) === false) {
                    $zip->close();
                    return false;
                }
            }
            
            $zip->close();
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
