<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssessmentMultipleSelectRequest;
use App\Http\Requests\UpdateAssessmentMultipleSelectRequest;
use App\Http\Traits\HandlesApiResponses;
use App\Models\AssessmentMultipleSelect;
use App\Services\AssessmentMultipleSelectService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssessmentMultipleSelectController extends Controller
{
    use HandlesApiResponses;

    public function __construct(
        private AssessmentMultipleSelectService $service
    ) {}

    /**
     * Display a listing of the assessments with pagination
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $perPage = min($perPage, 100); // Limit to 100 items per page

        $assessments = AssessmentMultipleSelect::with('assessment.units')
            ->paginate($perPage);

        return $this->successResponse(
            'Assessments retrieved successfully',
            $assessments
        );
    }

    /**
     * Display the specified assessment
     */
    public function show(AssessmentMultipleSelect $assessmentMultipleSelect): JsonResponse
    {
        $assessmentMultipleSelect->load('assessment.units');
        return $this->successResponse(
            'Assessment retrieved successfully',
            $assessmentMultipleSelect
        );
    }

    /**
     * Store a newly created assessment
     */
    public function store(StoreAssessmentMultipleSelectRequest $request): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($request) {
                $assessment = $this->service->create($request->validated());

                // Handle unit attachments if provided
                if ($request->has('unit_attachments')) {
                    $this->service->attachToUnits($assessment, $request->input('unit_attachments'));
                    $assessment = $assessment->fresh(['assessment.units']);
                }

                return $this->successResponse(
                    'Assessment created successfully',
                    $assessment,
                    201
                );
            },
            'create multiple select assessment'
        );
    }

    /**
     * Update the specified assessment
     */
    public function update(
        UpdateAssessmentMultipleSelectRequest $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        return $this->executeWithErrorHandling(
            function() use ($assessmentMultipleSelect, $request) {
                $assessment = $this->service->update($assessmentMultipleSelect, $request->validated());
                return $this->successResponse(
                    'Assessment updated successfully',
                    $assessment
                );
            },
            'update multiple select assessment',
            "assessment_id: {$assessmentMultipleSelect->id}"
        );
    }

    /**
     * Remove the specified assessment
     */
    public function destroy(int $id): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($id) {
                $assessmentMultipleSelect = AssessmentMultipleSelect::findOrFail($id);
                $this->service->delete($assessmentMultipleSelect);
                return $this->successResponse('Assessment deleted successfully');
            },
            'delete multiple select assessment',
            "assessment_id: {$id}"
        );
    }

    /**
     * Attach assessment to units
     */
    public function attachToUnits(
        Request $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        $request->validate([
            'unit_attachments' => 'required|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'required|integer|min:1',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($assessmentMultipleSelect, $request) {
                $this->service->attachToUnits($assessmentMultipleSelect, $request->input('unit_attachments'));
                return $this->successResponse('Assessment attached to units successfully');
            },
            'attach assessment to units',
            "assessment_id: {$assessmentMultipleSelect->id}"
        );
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(
        Request $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        $request->validate([
            'unit_ids' => 'nullable|array',
            'unit_ids.*' => 'integer|exists:units,id',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($assessmentMultipleSelect, $request) {
                $unitIds = $request->input('unit_ids', []);
                $this->service->detachFromUnits($assessmentMultipleSelect, $unitIds);
                return $this->successResponse('Assessment detached from units successfully');
            },
            'detach assessment from units',
            "assessment_id: {$assessmentMultipleSelect->id}"
        );
    }
}
