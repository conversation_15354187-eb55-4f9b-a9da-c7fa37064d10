<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateAiPromptRequest;
use App\Services\AiPromptService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\AiPrompt;

class AiPromptController extends Controller
{
    public function __construct(
        private AiPromptService $service
    ) {}

    /**
     * List all AI prompts with pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = min($request->get('per_page', 15), 100); // Max 100 per page
            $prompts = AiPrompt::paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'AI prompts retrieved successfully',
                'data' => $prompts
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve AI prompts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update existing prompt by type or create if doesn't exist
     *
     * @param UpdateAiPromptRequest $request
     * @param string $type
     * @return JsonResponse
     */
    public function update(UpdateAiPromptRequest $request, string $type): JsonResponse
    {
        try {
            $data = $request->validated();
            $prompt = $this->service->updateOrCreatePrompt($type, $data);

            return response()->json([
                'success' => true,
                'message' => 'AI prompt updated successfully',
                'data' => $prompt
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update AI prompt',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show a specific AI prompt by type
     *
     * @param string $type
     * @return JsonResponse
     */
    public function show(string $type): JsonResponse
    {
        try {
            $prompt = $this->service->getPromptByType($type);

            return response()->json([
                'success' => true,
                'message' => 'AI prompt retrieved successfully',
                'data' => $prompt
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'AI prompt not found for the specified type',
                'error' => "No prompt found for type: {$type}"
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve AI prompt',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
