<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportXlsxRequest;
use App\Http\Traits\HandlesApiResponses;
use App\Models\Unit;
use App\Services\XlsxImportAssessmentMultiSelectService;
use App\Services\XlsxImportAssessmentAiGapFillSentenceService;
use App\Services\XlsxImportAssessmentGapFillService;
use App\Services\XlsxImportAssessmentAnswerTheQuestionService;
use App\Enums\UnitType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ImportController extends Controller
{
    use HandlesApiResponses;

    /**
     * Maximum file size for import (in MB)
     */
    private const MAX_FILE_SIZE_MB = 25;

    /**
     * Maximum rows to process in a single batch
     */
    private const BATCH_SIZE = 100;

    public function __construct(
        private readonly XlsxImportAssessmentMultiSelectService $multiSelectImportService,
        private readonly XlsxImportAssessmentAiGapFillSentenceService $aiGapFillImportService,
        private readonly XlsxImportAssessmentGapFillService $gapFillImportService,
        private readonly XlsxImportAssessmentAnswerTheQuestionService $answerTheQuestionImportService
    ) {}

    /**
     * Import assessments from XLSX file for a specific unit
     */
    public function importXlsx(ImportXlsxRequest $request, Unit $unit): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($request, $unit) {
                $assessmentType = $unit->unit_type->value;
                $file = $request->getFile();

                // Validate file size
                $this->validateFileSize($file);

                // Get appropriate import service
                $importService = $this->getImportService($assessmentType);

                // Log import start
                Log::info('XLSX import started', [
                    'unit_id' => $unit->id,
                    'unit_title' => $unit->title,
                    'assessment_type' => $assessmentType,
                    'file_size' => $file->getSize(),
                    'original_name' => $file->getClientOriginalName()
                ]);

                // Process import
                $result = $importService->importAssessmentsForUnit($file, $unit);

                // Log import success
                Log::info('XLSX import completed', [
                    'unit_id' => $unit->id,
                    'assessments_created' => $result['assessments_created']
                ]);

                return $this->successResponse(
                    'XLSX import completed successfully',
                    [
                        'unit_id' => $unit->id,
                        'unit_title' => $unit->title,
                        'assessment_type' => $assessmentType,
                        'summary' => [
                            'assessments_created' => $result['assessments_created'],
                            'file_processed' => $file->getClientOriginalName(),
                            'processing_time' => $result['processing_time'] ?? null
                        ],
                    ],
                    201
                );
            },
            'import XLSX assessments',
            "unit_id: {$unit->id}, file: {$request->getFile()->getClientOriginalName()}"
        );
    }

    /**
     * Download XLSX template for import
     */
    public function downloadTemplate(string $assessmentType): Response
    {
        $this->validateAssessmentType($assessmentType);

        // Create new spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set sheet properties
        $sheet->setTitle('Import Template');
        $spreadsheet->getProperties()
            ->setCreator('English Game Backend')
            ->setTitle('Assessment Import Template')
            ->setDescription("Template for importing {$assessmentType} assessments");

        // Generate template data from appropriate service
        $templateData = $this->generateTemplateDataFromService($assessmentType);
        $headers = $templateData['headers'];
        $sampleData = $templateData['sample_data'];
        $filename = $templateData['filename'];

        // Set headers with formatting
        $this->setTemplateHeaders($sheet, $headers);

        // Set sample data
        $this->setTemplateSampleData($sheet, $sampleData, count($headers));

        // Format the sheet
        $this->formatTemplateSheet($sheet, $headers);

        // Generate response
        return $this->generateTemplateResponse($spreadsheet, $filename);
    }

    /**
     * Generate template data from the appropriate service based on assessment type
     */
    private function generateTemplateDataFromService(string $assessmentType): array
    {
        $importService = $this->getImportService($assessmentType);
        return $importService->generateTemplateData();
    }

    /**
     * Validate assessment type
     */
    private function validateAssessmentType(string $assessmentType): void
    {
        $validTypes = [
            UnitType::MULTIPLE_SELECT->value,
            UnitType::AI_GAP_FILL_SENTENCE->value,
            UnitType::GAP_FILL->value,
            UnitType::ANSWER_THE_QUESTION->value
        ];

        if (!in_array($assessmentType, $validTypes)) {
            throw new \InvalidArgumentException("Unsupported assessment type: {$assessmentType}");
        }
    }

    /**
     * Get import service for assessment type
     */
    private function getImportService(string $assessmentType)
    {
        return match($assessmentType) {
            UnitType::MULTIPLE_SELECT->value => $this->multiSelectImportService,
            UnitType::AI_GAP_FILL_SENTENCE->value => $this->aiGapFillImportService,
            UnitType::GAP_FILL->value => $this->gapFillImportService,
            UnitType::ANSWER_THE_QUESTION->value => $this->answerTheQuestionImportService,
            default => throw new \InvalidArgumentException("Unsupported assessment type: {$assessmentType}")
        };
    }

    /**
     * Validate file size
     */
    private function validateFileSize(\Illuminate\Http\UploadedFile $file): void
    {
        $maxSizeBytes = self::MAX_FILE_SIZE_MB * 1024 * 1024;

        if ($file->getSize() > $maxSizeBytes) {
            throw new \InvalidArgumentException(
                "File size ({$this->formatBytes($file->getSize())}) exceeds maximum allowed size of {$this->formatBytes($maxSizeBytes)}"
            );
        }
    }

    /**
     * Format bytes for human readable display
     */
    private function formatBytes(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Set template headers with formatting
     */
    private function setTemplateHeaders(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void
    {
        $columnIndex = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($columnIndex . '1', $header);

            // Make headers bold
            $sheet->getStyle($columnIndex . '1')->getFont()->setBold(true);

            // Set background color
            $sheet->getStyle($columnIndex . '1')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('FFE6E6FA');

            $columnIndex++;
        }
    }

    /**
     * Set template sample data
     */
    private function setTemplateSampleData(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $sampleData, int $headerCount): void
    {
        $rowIndex = 2;
        foreach ($sampleData as $row) {
            $columnIndex = 'A';
            foreach ($row as $value) {
                $sheet->setCellValue($columnIndex . $rowIndex, $value);
                $columnIndex++;
            }
            $rowIndex++;
        }
    }

    /**
     * Format template sheet
     */
    private function formatTemplateSheet(\PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void
    {
        // Auto-size columns
        foreach (range('A', $sheet->getHighestColumn()) as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Add borders to header row
        $headerRange = 'A1:' . chr(64 + count($headers)) . '1';
        $sheet->getStyle($headerRange)->getBorders()->getAllBorders()
            ->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
    }

    /**
     * Generate template response
     */
    private function generateTemplateResponse(Spreadsheet $spreadsheet, string $filename): Response
    {
        $writer = new Xlsx($spreadsheet);

        // Generate file content
        ob_start();
        $writer->save('php://output');
        $xlsxContent = ob_get_clean();

        return response($xlsxContent, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
            'Content-Length' => strlen($xlsxContent)
        ]);
    }

    /**
     * Get import statistics for a unit
     */
    public function getImportStats(Unit $unit): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($unit) {
                $assessmentCount = $unit->assessments()->count();
                $lastImport = $unit->updated_at;

                return $this->successResponse(
                    'Import statistics retrieved successfully',
                    [
                        'unit_id' => $unit->id,
                        'unit_title' => $unit->title,
                        'assessment_type' => $unit->unit_type->value,
                        'current_assessment_count' => $assessmentCount,
                        'last_updated' => $lastImport?->toISOString(),
                        'max_file_size' => $this->formatBytes(self::MAX_FILE_SIZE_MB * 1024 * 1024),
                        'batch_size' => self::BATCH_SIZE
                    ]
                );
            },
            'get import statistics',
            "unit_id: {$unit->id}"
        );
    }

    /**
     * Estimate processing time based on file size and row count
     */
    private function estimateProcessingTime(int $rowCount, int $fileSize): array
    {
        // Base processing time per row (in seconds)
        $timePerRow = 0.05; // 50ms per row

        // Additional time for large files (file size factor)
        $fileSizeFactor = min($fileSize / (1024 * 1024), 10) * 0.1; // Max 1 second for file size

        $estimatedSeconds = ($rowCount * $timePerRow) + $fileSizeFactor;

        return [
            'estimated_seconds' => round($estimatedSeconds, 1),
            'estimated_minutes' => round($estimatedSeconds / 60, 1),
            'factors' => [
                'row_count' => $rowCount,
                'time_per_row_ms' => $timePerRow * 1000,
                'file_size_factor_s' => round($fileSizeFactor, 2)
            ]
        ];
    }

    /**
     * Get import recommendations based on file characteristics
     */
    private function getImportRecommendations(int $rowCount, int $fileSize): array
    {
        $recommendations = [];

        if ($rowCount > 500) {
            $recommendations[] = [
                'type' => 'performance',
                'message' => 'Large import detected. Consider processing during off-peak hours.',
                'severity' => 'info'
            ];
        }

        if ($fileSize > 10 * 1024 * 1024) { // 10MB
            $recommendations[] = [
                'type' => 'memory',
                'message' => 'Large file size. Import will use streaming processing for memory efficiency.',
                'severity' => 'info'
            ];
        }

        if ($rowCount > 1000) {
            $recommendations[] = [
                'type' => 'timeout',
                'message' => 'Very large import. Please ensure stable internet connection.',
                'severity' => 'warning'
            ];
        }

        if (empty($recommendations)) {
            $recommendations[] = [
                'type' => 'success',
                'message' => 'File size and row count are optimal for import.',
                'severity' => 'success'
            ];
        }

        return $recommendations;
    }

    /**
     * Perform detailed validation with row-level error reporting
     */
    private function performDetailedValidation($importService, $file, Unit $unit): array
    {
        // This would require enhancing the import service to return detailed validation
        // For now, we'll call the existing validation and enhance the response

        try {
            $basicValidation = $importService->validateAssessmentsXlsx($file, $unit);

            return [
                'unit_id' => $unit->id,
                'unit_title' => $unit->title,
                'assessment_type' => $unit->unit_type->value,
                'validation_status' => 'passed',
                'summary' => $basicValidation,
                'errors' => [],
                'warnings' => [],
                'file_info' => [
                    'name' => $file->getClientOriginalName(),
                    'size' => $this->formatBytes($file->getSize())
                ]
            ];
        } catch (\Exception $e) {
            return [
                'unit_id' => $unit->id,
                'unit_title' => $unit->title,
                'assessment_type' => $unit->unit_type->value,
                'validation_status' => 'failed',
                'errors' => [
                    [
                        'type' => 'validation_error',
                        'message' => $e->getMessage(),
                        'severity' => 'error'
                    ]
                ],
                'warnings' => [],
                'file_info' => [
                    'name' => $file->getClientOriginalName(),
                    'size' => $this->formatBytes($file->getSize())
                ]
            ];
        }
    }

    /**
     * Get import history for a unit
     */
    public function getImportHistory(Unit $unit): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($unit) {
                // Get recent assessment creation timestamps as proxy for import history
                $recentAssessments = $unit->assessments()
                    ->with('itemable')
                    ->orderBy('created_at', 'desc')
                    ->limit(50)
                    ->get()
                    ->groupBy(function($assessment) {
                        return $assessment->created_at->format('Y-m-d H:i');
                    });

                $importHistory = $recentAssessments->map(function($group, $timestamp) {
                    return [
                        'timestamp' => $timestamp,
                        'assessments_count' => $group->count(),
                        'assessment_types' => $group->pluck('itemable_type')->unique()->values(),
                        'created_at' => $group->first()->created_at->toISOString()
                    ];
                })->values();

                return $this->successResponse(
                    'Import history retrieved successfully',
                    [
                        'unit_id' => $unit->id,
                        'unit_title' => $unit->title,
                        'total_assessments' => $unit->assessments()->count(),
                        'import_history' => $importHistory,
                        'statistics' => [
                            'last_import' => $unit->updated_at?->toISOString(),
                            'total_imports_detected' => $importHistory->count()
                        ]
                    ]
                );
            },
            'get import history',
            "unit_id: {$unit->id}"
        );
    }
}
