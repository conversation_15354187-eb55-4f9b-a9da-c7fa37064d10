<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Enums\UnitType;

class SettingsController extends Controller
{
    public function adminSettings()
    {
        $settings = [
            'assessment_types' => UnitType::getSettings(),
            'ai_assessment_types' => UnitType::getAISettings()
        ];

        return response()->json([
            'success' => true,
            'message' => 'Admin settings fetched successfully',
            'data' => $settings
        ]);
    }
}
