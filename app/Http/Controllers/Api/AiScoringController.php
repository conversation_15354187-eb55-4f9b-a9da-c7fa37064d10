<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\TestPromptRequest;
use App\Models\Unit;
use App\Services\AiPromptService;
use App\Services\GeminiService;
use App\Enums\UnitType;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class AiScoringController extends Controller
{
    public function __construct(
        private AiPromptService $aiPromptService,
        private GeminiService $geminiService
    ) {}

    /**
     * Test prompt generation and AI response
     *
     * @param TestPromptRequest $request
     * @return JsonResponse
     */
    public function testPrompt(TestPromptRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            $unitId = $validatedData['unit_id'];
            $requestAnswers = $validatedData['answers'];

            $result = $this->aiPromptService->getResult($unitId, $requestAnswers);

            return response()->json([
                'success' => true,
                'message' => 'AI test prompt generated successfully',
                'data' => $result
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found',
                'error' => $e->getMessage()
            ], 404);
        } catch (InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data',
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('AI Test Prompt Error: ' . $e->getMessage(), [
                'unit_id' => $unitId ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate test prompt',
                'error' => 'An unexpected error occurred while processing your request'
            ], 500);
        }
    }

    /**
     * List AI assessments by type
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function listAiAssessment(string $type): JsonResponse
    {
        try {
            // 1. Validate that the provided type is a valid AI assessment type
            $validAiTypes = UnitType::getAiAssessmentType();

            if (!in_array($type, $validAiTypes)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid AI assessment type',
                    'error' => "Type '{$type}' is not a valid AI assessment type. Valid types are: " . implode(', ', $validAiTypes)
                ], 400);
            }

            // 2. Use Eloquent relationships to get units of the specified type with their assessments
            $units = Unit::where('unit_type', $type)
                ->with(['assessments.itemable'])
                ->orderBy('created_at', 'asc')
                ->get();

            // 3. Flatten all assessments from all units and format the response
            $formattedResults = collect();

            foreach ($units as $unit) {
                foreach ($unit->assessments as $assessment) {
                    $formattedResults->push([
                        'unit_id' => $unit->id,
                        'question' => $assessment->getQuestion(),
                        'fill_position' => $assessment->getFillPosition(),
                        'assessment_id' => $assessment->id
                    ]);

                    // Stop if we reach the limit
                    if ($formattedResults->count() >= 25) {
                        break 2; // Break out of both loops
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'AI assessments retrieved successfully',
                'data' => $formattedResults->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('List AI Assessment Error: ' . $e->getMessage(), [
                'type' => $type ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve AI assessments',
                'error' => 'An unexpected error occurred while processing your request'
            ], 500);
        }
    }
}
