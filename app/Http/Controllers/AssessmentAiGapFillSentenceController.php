<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssessmentAiGapFillSentenceRequest;
use App\Http\Requests\UpdateAssessmentAiGapFillSentenceRequest;
use App\Http\Traits\HandlesApiResponses;
use App\Models\AssessmentAiGapFillSentence;
use App\Services\AssessmentAiGapFillSentenceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssessmentAiGapFillSentenceController extends Controller
{
    use HandlesApiResponses;

    public function __construct(
        private AssessmentAiGapFillSentenceService $service
    ) {}

    /**
     * Display a listing of the assessments with pagination
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $perPage = min($perPage, 100); // Limit to 100 items per page

        $assessments = AssessmentAiGapFillSentence::with('assessment.units')
            ->paginate($perPage);

        return $this->successResponse(
            'AI Gap Fill Sentence assessments retrieved successfully',
            $assessments
        );
    }

    /**
     * Display the specified assessment
     */
    public function show(AssessmentAiGapFillSentence $assessmentAiGapFillSentence): JsonResponse
    {
        $assessmentAiGapFillSentence->load('assessment.units');
        return $this->successResponse(
            'AI Gap Fill Sentence assessment retrieved successfully',
            $assessmentAiGapFillSentence
        );
    }

    /**
     * Store a newly created assessment
     */
    public function store(StoreAssessmentAiGapFillSentenceRequest $request): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($request) {
                $assessment = $this->service->create($request->validated());

                // Handle unit attachments if provided
                if ($request->has('unit_attachments')) {
                    $this->service->attachToUnits($assessment, $request->input('unit_attachments'));
                    $assessment = $assessment->fresh(['assessment.units']);
                }

                return $this->successResponse(
                    'AI Gap Fill Sentence assessment created successfully',
                    $assessment,
                    201
                );
            },
            'create AI gap fill sentence assessment'
        );
    }

    /**
     * Update the specified assessment
     */
    public function update(
        UpdateAssessmentAiGapFillSentenceRequest $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        return $this->executeWithErrorHandling(
            function() use ($assessmentAiGapFillSentence, $request) {
                $assessment = $this->service->update($assessmentAiGapFillSentence, $request->validated());
                return $this->successResponse(
                    'AI Gap Fill Sentence assessment updated successfully',
                    $assessment
                );
            },
            'update AI gap fill sentence assessment',
            "assessment_id: {$assessmentAiGapFillSentence->id}"
        );
    }

    /**
     * Remove the specified assessment
     */
    public function destroy(int $id): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($id) {
                $assessmentAiGapFillSentence = AssessmentAiGapFillSentence::findOrFail($id);
                $this->service->delete($assessmentAiGapFillSentence);
                return $this->successResponse('AI Gap Fill Sentence assessment deleted successfully');
            },
            'delete AI gap fill sentence assessment',
            "assessment_id: {$id}"
        );
    }

    /**
     * Attach assessment to units
     */
    public function attachToUnits(
        Request $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        $request->validate([
            'unit_attachments' => 'required|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'required|integer|min:1',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($assessmentAiGapFillSentence, $request) {
                $this->service->attachToUnits($assessmentAiGapFillSentence, $request->input('unit_attachments'));
                return $this->successResponse('AI Gap Fill Sentence assessment attached to units successfully');
            },
            'attach AI gap fill sentence assessment to units',
            "assessment_id: {$assessmentAiGapFillSentence->id}"
        );
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(
        Request $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        $request->validate([
            'unit_ids' => 'nullable|array',
            'unit_ids.*' => 'integer|exists:units,id',
        ]);

        return $this->executeWithErrorHandling(
            function() use ($assessmentAiGapFillSentence, $request) {
                $unitIds = $request->input('unit_ids', []);
                $this->service->detachFromUnits($assessmentAiGapFillSentence, $unitIds);
                return $this->successResponse('AI Gap Fill Sentence assessment detached from units successfully');
            },
            'detach AI gap fill sentence assessment from units',
            "assessment_id: {$assessmentAiGapFillSentence->id}"
        );
    }
}
