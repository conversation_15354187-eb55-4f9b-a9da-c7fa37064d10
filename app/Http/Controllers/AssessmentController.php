<?php

namespace App\Http\Controllers;

use App\Http\Traits\HandlesApiResponses;
use App\Enums\UnitType;
use App\Models\Assessment;
use App\Models\AssessmentMultipleSelect;
use App\Models\AssessmentAiGapFillSentence;
use App\Models\AssessmentGapFill;
use App\Models\AssessmentAnswerTheQuestion;
use App\Services\AssessmentMultipleSelectService;
use App\Services\AssessmentAiGapFillSentenceService;
use App\Services\AssessmentGapFillService;
use App\Services\AssessmentAnswerTheQuestionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Storage\Http\Resources\FileResource;
use Modules\Storage\Services\StorageService;

class AssessmentController extends Controller
{
    use HandlesApiResponses;

    public function __construct(
        private readonly AssessmentMultipleSelectService $multipleSelectService,
        private readonly AssessmentAiGapFillSentenceService $aiGapFillService,
        private readonly AssessmentGapFillService $gapFillService,
        private readonly AssessmentAnswerTheQuestionService $answerTheQuestionService
    ) {}

    /**
     * Get the model class for a given unit type
     */
    private function getModelClass(UnitType $unitType): string
    {
        return match ($unitType) {
            UnitType::MULTIPLE_SELECT => AssessmentMultipleSelect::class,
            UnitType::AI_GAP_FILL_SENTENCE => AssessmentAiGapFillSentence::class,
            UnitType::GAP_FILL => AssessmentGapFill::class,
            UnitType::ANSWER_THE_QUESTION => AssessmentAnswerTheQuestion::class,
        };
    }

    /**
     * Get the service instance for a given unit type
     */
    private function getService(UnitType $unitType)
    {
        return match ($unitType) {
            UnitType::MULTIPLE_SELECT => $this->multipleSelectService,
            UnitType::AI_GAP_FILL_SENTENCE => $this->aiGapFillService,
            UnitType::GAP_FILL => $this->gapFillService,
            UnitType::ANSWER_THE_QUESTION => $this->answerTheQuestionService,
        };
    }

    /**
     * Get the service instance for scoring by model class name
     */
    private function getScoringService(string $modelClass)
    {
        return match ($modelClass) {
            'App\\Models\\AssessmentMultipleSelect' => $this->multipleSelectService,
            'App\\Models\\AssessmentGapFill' => $this->gapFillService,
            'App\\Models\\AssessmentAiGapFillSentence' => $this->aiGapFillService,
            'App\\Models\\AssessmentAnswerTheQuestion' => $this->answerTheQuestionService,
            default => throw new \InvalidArgumentException("Unsupported assessment type: {$modelClass}")
        };
    }

    /**
     * Validate and parse assessment type
     */
    private function parseAssessmentType(string $type): UnitType
    {
        $normalizedType = str_replace('-', '_', $type);
        return UnitType::from($normalizedType);
    }

    /**
     * Get validation rules for a given unit type
     */
    private function getValidationRules(UnitType $unitType, bool $isUpdate = false): array
    {
        $prefix = $isUpdate ? 'sometimes|' : '';

        $baseRules = [
            'unit_attachments' => 'nullable|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'nullable|integer|min:1'
        ];

        $typeSpecificRules = match ($unitType) {
            UnitType::MULTIPLE_SELECT => [
                'question' => $prefix . 'required|string|max:1000',
                'answer_list' => $prefix . 'required|array|min:2|max:10',
                'answer_list.*' => 'required|string|max:500',
                'correct_answer_indexes' => $prefix . 'required|array|min:1',
                'correct_answer_indexes.*' => 'required|integer|min:0',
                'explanations' => 'nullable|array',
                'explanations.*' => 'nullable|string|max:1000',
            ],
            UnitType::AI_GAP_FILL_SENTENCE => [
                'question' => $prefix . 'required|string|max:1000|regex:/.*_.*/',
                'context' => $prefix . 'required|string|max:2000',
            ],
            UnitType::GAP_FILL => [
                'question' => $prefix . 'required|string|max:1000',
                'explanation' => 'nullable|string|max:2000',
                'correct_answers' => $prefix . 'required|array|min:1',
                'correct_answers.*' => 'required|string|max:500',
            ],
            UnitType::ANSWER_THE_QUESTION => [
                'question' => $prefix . 'required|string|max:1000',
                'explanation' => 'nullable|string|max:2000',
                'correct_answers' => $prefix . 'required|array|min:1',
                'correct_answers.*' => 'required|string|max:500',
            ],
        };

        return array_merge($typeSpecificRules, $isUpdate ? [] : $baseRules);
    }

    /**
     * Store a newly created assessment based on type
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string'
        ]);

        return $this->executeWithErrorHandling(
            function() use ($request) {
                $unitType = $this->parseAssessmentType($request->input('type'));

                // Validate request data based on assessment type
                $validated = $request->validate($this->getValidationRules($unitType));

                // Get the appropriate service and create assessment
                $service = $this->getService($unitType);
                $assessment = $service->create($validated);

                // Handle unit attachments if provided
                if ($request->has('unit_attachments')) {
                    $service->attachToUnits($assessment, $request->input('unit_attachments'));
                    $assessment = $assessment->fresh(['assessment.units']);
                }

                return $this->successResponse(
                    'Assessment created successfully',
                    $assessment,
                    201
                );
            },
            'create assessment',
            "type: {$request->input('type')}"
        );
    }

    /**
     * Update assessment based on type
     */
    public function update(Request $request, string $type, int $id): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($type, $id, $request) {
                $unitType = $this->parseAssessmentType($type);

                // Find the assessment model instance
                $modelClass = $this->getModelClass($unitType);
                $assessment = $modelClass::findOrFail($id);

                // Validate request data based on assessment type (update rules)
                $validated = $request->validate($this->getValidationRules($unitType, true));

                // Get the appropriate service and update assessment
                $service = $this->getService($unitType);
                $assessment = $service->update($assessment, $validated);

                return $this->successResponse(
                    'Assessment updated successfully',
                    $assessment
                );
            },
            'update assessment',
            "type: {$type}, id: {$id}"
        );
    }

    /**
     * Delete assessment based on type
     */
    public function destroy(string $type, int $id): JsonResponse
    {
        return $this->executeWithErrorHandling(
            function() use ($type, $id) {
                $unitType = $this->parseAssessmentType($type);

                // Find the assessment model instance
                $modelClass = $this->getModelClass($unitType);
                $assessment = $modelClass::findOrFail($id);

                // Get the appropriate service and delete assessment
                $service = $this->getService($unitType);
                $service->delete($assessment);

                return $this->successResponse('Assessment deleted successfully');
            },
            'delete assessment',
            "type: {$type}, id: {$id}"
        );
    }

    /**
     * Score any type of assessment
     */
    public function score(Request $request, string $type): JsonResponse
    {
        $request->validate([
            'assessment_id' => 'required|integer',
            'answer' => 'required'
        ]);

        $unitType = $this->parseAssessmentType($type);

        return $this->executeWithErrorHandling(
            function() use ($request, $unitType) {
                $assessmentId = $request->input('assessment_id');
                $userAnswer = $request->input('answer');
                $modelClass = $this->getModelClass($unitType);
                $scoringService = $this->getScoringService($modelClass);

                $record = $modelClass::findOrFail($assessmentId);

                $scoringResult = $scoringService->score($record, $userAnswer);

                return $this->successResponse(
                    'Assessment scored successfully',
                    $scoringResult
                );
            },
            'score assessment',
            "assessment_id: {$request->input('assessment_id')}"
        );
    }

    public function attachFile(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'type' => 'required|string',
            'url' => 'required|string'
        ]);

        $url = $request->input('url');
        $type = $request->input('type');

        $assessment = Assessment::findOrFail($id);
        $assessmentDetail = $assessment->itemable;

        $storageService = app(StorageService::class);
        $file = $storageService->moveFileFromTempToTarget($url, $assessmentDetail, $type);

        return $this->successResponse('Create file success', new FileResource($file));
    }

    public function detachFile(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'file_id' => 'required'
        ]);

        $fileId = $request->input('file_id');

        $assessment = Assessment::findOrFail($id);
        $assessmentDetail = $assessment->itemable;

        $storageService = app(StorageService::class);

        $file = $assessmentDetail->rawFiles()->where('id', $fileId)->first();
        $storageService->deleteFile($file);

        return $this->successResponse('File removed successfully');
    }

    /**
     * Score assessment by determining its type and calling appropriate service
     */
    private function scoreAssessmentByType(Assessment $assessment, $userAnswer)
    {
        $itemableType = $assessment->itemable_type;
        $itemable = $assessment->itemable;

        // Validate that answer is array (required for all assessment types)
        $this->validateAnswerFormat($userAnswer);

        // Get appropriate service and score
        $service = $this->getScoringService($itemableType);
        return $service->score($itemable, $userAnswer);
    }

    /**
     * Validate that the user answer is in the correct format for scoring
     */
    private function validateAnswerFormat($userAnswer): void
    {
        if (!is_array($userAnswer)) {
            throw new \InvalidArgumentException('Assessment answers must be provided as an array');
        }
    }
}
