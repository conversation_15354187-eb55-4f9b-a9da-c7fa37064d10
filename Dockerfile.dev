# Development Dockerfile for Laravel Octane with FrankenPHP
FROM dunglas/frankenphp

# Install system dependencies including Node.js for file watching
RUN apt-get update && apt-get install -y \
    git \
    zip \
    unzip \
    curl \
    nodejs \
    npm \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions required for Laravel Octane development
RUN install-php-extensions \
    pcntl \
    redis \
    pdo_mysql \
    mysqli \
    zip \
    gd \
    bcmath \
    opcache \
    intl \
    exif \
    xdebug

# Configure Xdebug for development
RUN echo "xdebug.mode=debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_host=host.docker.internal" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_port=9003" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.start_with_request=yes" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set working directory
WORKDIR /app

# Copy application files first
COPY . .

# Install PHP dependencies with dev packages for development
RUN composer install --no-interaction --prefer-dist --dev

# Copy package.json if exists for Node dependencies
RUN if [ -f package.json ]; then npm install; fi

# Install Chokidar for file watching in development
RUN npm install --save-dev chokidar

# Set proper permissions for Laravel directories
RUN chown -R www-data:www-data /app \
    && chmod -R 775 /app/storage \
    && chmod -R 775 /app/bootstrap/cache

# Create directories for FrankenPHP configuration
RUN mkdir -p /var/www/html/config /var/www/html/data \
    && chown -R www-data:www-data /var/www/html

# Set environment variables for FrankenPHP
ENV XDG_CONFIG_HOME=/var/www/html/config
ENV XDG_DATA_HOME=/var/www/html/data

# Expose port 8000
EXPOSE 8000

# Development command with file watching, single worker, and low max requests for faster reloads
CMD ["php", "artisan", "octane:frankenphp", "--host=0.0.0.0", "--port=8000", "--workers=1", "--max-requests=1", "--watch"]