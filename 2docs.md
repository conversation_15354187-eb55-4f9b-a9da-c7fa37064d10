
Laravel Octane
Introduction
Laravel Octane supercharges your application's performance by serving your application using high-powered application servers, including FrankenPHP, Open Swoole, Swoole, and RoadRunner. Octane boots your application once, keeps it in memory, and then feeds it requests at supersonic speeds.

Installation
Octane may be installed via the Composer package manager:

composer require laravel/octane

After installing Octane, you may execute the octane:install Artisan command, which will install Octane's configuration file into your application:

php artisan octane:install

Server Prerequisites
FrankenPHP
FrankenPHP is a PHP application server, written in Go, that supports modern web features like early hints, Brotli, and Zstandard compression. When you install Octane and choose FrankenPHP as your server, Octane will automatically download and install the FrankenPHP binary for you.

FrankenPHP via Laravel Sail
If you plan to develop your application using Laravel Sail, you should run the following commands to install Octane and FrankenPHP:

./vendor/bin/sail up

./vendor/bin/sail composer require laravel/octane

Next, you should use the octane:install Artisan command to install the FrankenPHP binary:

./vendor/bin/sail artisan octane:install --server=frankenphp

Finally, add a SUPERVISOR_PHP_COMMAND environment variable to the laravel.test service definition in your application's docker-compose.yml file. This environment variable will contain the command that <PERSON><PERSON> will use to serve your application using Octane instead of the PHP development server:

services:
  laravel.test:
    environment:
      SUPERVISOR_PHP_COMMAND: "/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan octane:start --server=frankenphp --host=0.0.0.0 --admin-port=2019 --port='${APP_PORT:-80}'"
      XDG_CONFIG_HOME:  /var/www/html/config
      XDG_DATA_HOME:  /var/www/html/data

To enable HTTPS, HTTP/2, and HTTP/3, apply these modifications instead:

services:
  laravel.test:
    ports:
        - '${APP_PORT:-80}:80'
        - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        - '443:443'
        - '443:443/udp'
    environment:
      SUPERVISOR_PHP_COMMAND: "/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan octane:start --host=localhost --port=443 --admin-port=2019 --https"
      XDG_CONFIG_HOME:  /var/www/html/config
      XDG_DATA_HOME:  /var/www/html/data

Typically, you should access your FrankenPHP Sail application via https://localhost, as using https://127.0.0.1 requires additional configuration and is discouraged.

FrankenPHP via Docker
Using FrankenPHP's official Docker images can offer improved performance and the use of additional extensions not included with static installations of FrankenPHP. In addition, the official Docker images provide support for running FrankenPHP on platforms it doesn't natively support, such as Windows. FrankenPHP's official Docker images are suitable for both local development and production usage.

You may use the following Dockerfile as a starting point for containerizing your FrankenPHP powered Laravel application:

FROM dunglas/frankenphp

RUN install-php-extensions \
    pcntl
    # Add other PHP extensions here...

COPY . /app

ENTRYPOINT ["php", "artisan", "octane:frankenphp"]

Then, during development, you may utilize the following Docker Compose file to run your application:

# compose.yaml
services:
  frankenphp:
    build:
      context: .
    entrypoint: php artisan octane:frankenphp --workers=1 --max-requests=1
    ports:
      - "8000:8000"
    volumes:
      - .:/app

If the --log-level option is explicitly passed to the php artisan octane:start command, Octane will use FrankenPHP's native logger and, unless configured differently, will produce structured JSON logs.

You may consult the official FrankenPHP documentation for more information on running FrankenPHP with Docker.

Custom Caddyfile Configuration
When using FrankenPHP, you may specify a custom Caddyfile using the --caddyfile option when starting Octane:

php artisan octane:start --server=frankenphp --caddyfile=/path/to/your/Caddyfile

This allows you to customize FrankenPHP's configuration beyond the default settings, such as adding custom middleware, configuring advanced routing, or setting up custom directives. You may consult the official Caddy documentation for more information on Caddyfile syntax and configuration options.

RoadRunner
RoadRunner is powered by the RoadRunner binary, which is built using Go. The first time you start a RoadRunner based Octane server, Octane will offer to download and install the RoadRunner binary for you.

RoadRunner via Laravel Sail
If you plan to develop your application using Laravel Sail, you should run the following commands to install Octane and RoadRunner:

./vendor/bin/sail up

./vendor/bin/sail composer require laravel/octane spiral/roadrunner-cli spiral/roadrunner-http

Next, you should start a Sail shell and use the rr executable to retrieve the latest Linux based build of the RoadRunner binary:

./vendor/bin/sail shell

# Within the Sail shell...
./vendor/bin/rr get-binary

Then, add a SUPERVISOR_PHP_COMMAND environment variable to the laravel.test service definition in your application's docker-compose.yml file. This environment variable will contain the command that Sail will use to serve your application using Octane instead of the PHP development server:

services:
  laravel.test:
    environment:
      SUPERVISOR_PHP_COMMAND: "/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan octane:start --server=roadrunner --host=0.0.0.0 --rpc-port=6001 --port='${APP_PORT:-80}'"

Finally, ensure the rr binary is executable and build your Sail images:

chmod +x ./rr

./vendor/bin/sail build --no-cache

Swoole
If you plan to use the Swoole application server to serve your Laravel Octane application, you must install the Swoole PHP extension. Typically, this can be done via PECL:

pecl install swoole

Open Swoole
If you want to use the Open Swoole application server to serve your Laravel Octane application, you must install the Open Swoole PHP extension. Typically, this can be done via PECL:

pecl install openswoole

Using Laravel Octane with Open Swoole grants the same functionality provided by Swoole, such as concurrent tasks, ticks, and intervals.

Swoole via Laravel Sail
Before serving an Octane application via Sail, ensure you have the latest version of Laravel Sail and execute ./vendor/bin/sail build --no-cache within your application's root directory.

Alternatively, you may develop your Swoole based Octane application using Laravel Sail, the official Docker based development environment for Laravel. Laravel Sail includes the Swoole extension by default. However, you will still need to adjust the docker-compose.yml file used by Sail.

To get started, add a SUPERVISOR_PHP_COMMAND environment variable to the laravel.test service definition in your application's docker-compose.yml file. This environment variable will contain the command that Sail will use to serve your application using Octane instead of the PHP development server:

services:
  laravel.test:
    environment:
      SUPERVISOR_PHP_COMMAND: "/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan octane:start --server=swoole --host=0.0.0.0 --port='${APP_PORT:-80}'"

Finally, build your Sail images:

./vendor/bin/sail build --no-cache

Swoole Configuration
Swoole supports a few additional configuration options that you may add to your octane configuration file if necessary. Because they rarely need to be modified, these options are not included in the default configuration file:

'swoole' => [
    'options' => [
        'log_file' => storage_path('logs/swoole_http.log'),
        'package_max_length' => 10 * 1024 * 1024,
    ],
],

Serving Your Application
The Octane server can be started via the octane:start Artisan command. By default, this command will utilize the server specified by the server configuration option of your application's octane configuration file:

php artisan octane:start

By default, Octane will start the server on port 8000, so you may access your application in a web browser via http://localhost:8000.

Serving Your Application via HTTPS
By default, applications running via Octane generate links prefixed with http://. The OCTANE_HTTPS environment variable, used within your application's config/octane.php configuration file, can be set to true when serving your application via HTTPS. When this configuration value is set to true, Octane will instruct Laravel to prefix all generated links with https://:

'https' => env('OCTANE_HTTPS', false),

Serving Your Application via Nginx
If you aren't quite ready to manage your own server configuration or aren't comfortable configuring all of the various services needed to run a robust Laravel Octane application, check out Laravel Cloud, which offers fully-managed Laravel Octane support.

In production environments, you should serve your Octane application behind a traditional web server such as Nginx or Apache. Doing so will allow the web server to serve your static assets such as images and stylesheets, as well as manage your SSL certificate termination.

In the Nginx configuration example below, Nginx will serve the site's static assets and proxy requests to the Octane server that is running on port 8000:

map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {
    listen 80;
    listen [::]:80;
    server_name domain.com;
    server_tokens off;
    root /home/<USER>/domain.com/public;

    index index.php;

    charset utf-8;

    location /index.php {
        try_files /not_exists @octane;
    }

    location / {
        try_files $uri $uri/ @octane;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    access_log off;
    error_log  /var/log/nginx/domain.com-error.log error;

    error_page 404 /index.php;

    location @octane {
        set $suffix "";

        if ($uri = /index.php) {
            set $suffix ?$query_string;
        }

        proxy_http_version 1.1;
        proxy_set_header Host $http_host;
        proxy_set_header Scheme $scheme;
        proxy_set_header SERVER_PORT $server_port;
        proxy_set_header REMOTE_ADDR $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        proxy_pass http://127.0.0.1:8000$suffix;
    }
}

Watching for File Changes
Since your application is loaded in memory once when the Octane server starts, any changes to your application's files will not be reflected when you refresh your browser. For example, route definitions added to your routes/web.php file will not be reflected until the server is restarted. For convenience, you may use the --watch flag to instruct Octane to automatically restart the server on any file changes within your application:

php artisan octane:start --watch

Before using this feature, you should ensure that Node is installed within your local development environment. In addition, you should install the Chokidar file-watching library within your project:

npm install --save-dev chokidar

You may configure the directories and files that should be watched using the watch configuration option within your application's config/octane.php configuration file.
